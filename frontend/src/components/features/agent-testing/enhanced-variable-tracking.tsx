"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  Settings, 
  CheckCircle, 
  Clock, 
  ArrowRight, 
  Users, 
  User, 
  Cpu, 
  ArrowRightLeft,
  Network,
  Zap,
  Eye,
  EyeOff
} from "lucide-react";

interface VariablePlaceholder {
  id: string;
  placeholderName: string;
  sourceStep: string;
  sourceAgent: string;
  semanticDescription: string;
  value: string | null;
  resolvedAt: string | null;
  stepIndex: number;
  communicationType: 'inter-agent' | 'user-input' | 'system' | 'internal';
  destinationAgents: string[];
  dependsOn: string[];
  dataFlowChain: Array<{
    agent: string;
    step: string;
    timestamp: string;
  }>;
  contextDependencies: string[];
  isSharedBetweenAgents: boolean;
}

interface EnhancedVariableTrackingProps {
  variablePlaceholders: VariablePlaceholder[];
  websocketStatus: 'disconnected' | 'connecting' | 'connected';
  isExecuting: boolean;
  className?: string;
}

export function EnhancedVariableTracking({ 
  variablePlaceholders, 
  websocketStatus, 
  isExecuting,
  className = "" 
}: EnhancedVariableTrackingProps) {
  const [activeTab, setActiveTab] = useState("all");
  const [showDetails, setShowDetails] = useState<string[]>([]);

  // Group variables by communication type
  const groupedVariables = {
    all: variablePlaceholders,
    'inter-agent': variablePlaceholders.filter(v => v.communicationType === 'inter-agent'),
    'user-input': variablePlaceholders.filter(v => v.communicationType === 'user-input'),
    'system': variablePlaceholders.filter(v => v.communicationType === 'system'),
    'internal': variablePlaceholders.filter(v => v.communicationType === 'internal')
  };

  const toggleDetails = (id: string) => {
    setShowDetails(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const getCommunicationTypeConfig = (type: string) => {
    const configs = {
      'inter-agent': {
        icon: <ArrowRightLeft className="h-4 w-4" />,
        label: '代理间通信',
        color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300',
        borderColor: 'border-blue-200 dark:border-blue-800',
        bgColor: 'bg-blue-50 dark:bg-blue-950/30'
      },
      'user-input': {
        icon: <User className="h-4 w-4" />,
        label: '用户输入',
        color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300',
        borderColor: 'border-green-200 dark:border-green-800',
        bgColor: 'bg-green-50 dark:bg-green-950/30'
      },
      'system': {
        icon: <Cpu className="h-4 w-4" />,
        label: '系统变量',
        color: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300',
        borderColor: 'border-purple-200 dark:border-purple-800',
        bgColor: 'bg-purple-50 dark:bg-purple-950/30'
      },
      'internal': {
        icon: <Settings className="h-4 w-4" />,
        label: '内部变量',
        color: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300',
        borderColor: 'border-gray-200 dark:border-gray-800',
        bgColor: 'bg-gray-50 dark:bg-gray-950/30'
      }
    };
    return configs[type as keyof typeof configs] || configs.internal;
  };

  const renderDataFlowVisualization = (placeholder: VariablePlaceholder) => {
    if (!placeholder.isSharedBetweenAgents || placeholder.dataFlowChain.length <= 1) {
      return null;
    }

    return (
      <div className="mt-3 p-3 bg-muted/30 rounded-lg border">
        <div className="flex items-center space-x-2 mb-2">
          <Network className="h-4 w-4 text-primary" />
          <span className="text-sm font-medium">数据流向</span>
        </div>
        <div className="flex items-center space-x-2 overflow-x-auto">
          {placeholder.dataFlowChain.map((flow, index) => (
            <div key={index} className="flex items-center space-x-2 flex-shrink-0">
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                  <Users className="h-4 w-4 text-primary" />
                </div>
                <span className="text-xs text-muted-foreground mt-1 max-w-16 truncate">
                  {flow.agent}
                </span>
              </div>
              {index < placeholder.dataFlowChain.length - 1 && (
                <ArrowRight className="h-4 w-4 text-muted-foreground" />
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderVariableCard = (placeholder: VariablePlaceholder) => {
    const config = getCommunicationTypeConfig(placeholder.communicationType);
    const isResolved = Boolean(placeholder.value);
    const showDetailView = showDetails.includes(placeholder.id);

    return (
      <motion.div
        key={placeholder.id}
        layout
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        className={`p-4 rounded-lg border transition-all ${
          isResolved 
            ? 'bg-green-50 border-green-200 dark:bg-green-950/30 dark:border-green-800/50'
            : config.bgColor + ' ' + config.borderColor
        }`}
      >
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1 min-w-0">
            {/* Header */}
            <div className="flex items-center gap-2 mb-2">
              <code className="text-sm font-mono bg-background/50 px-2 py-1 rounded border">
                {placeholder.placeholderName}
              </code>
              <Badge variant="outline" className="text-xs">
                步骤 {placeholder.stepIndex + 1}
              </Badge>
              <Badge className={`text-xs ${config.color}`}>
                {config.icon}
                <span className="ml-1">{config.label}</span>
              </Badge>
            </div>

            {/* Source Info */}
            <div className="text-xs text-muted-foreground mb-2">
              <span className="font-medium">来源:</span> {placeholder.sourceAgent} → {placeholder.sourceStep}
              {placeholder.semanticDescription && (
                <span className="ml-2">• {placeholder.semanticDescription}</span>
              )}
            </div>

            {/* Inter-agent Communication Summary */}
            {placeholder.isSharedBetweenAgents && (
              <div className="mb-2">
                <div className="flex items-center space-x-2 text-xs">
                  <ArrowRightLeft className="h-3 w-3 text-blue-500" />
                  <span className="text-blue-700 dark:text-blue-300 font-medium">
                    跨代理通信 ({placeholder.destinationAgents.length} 个目标)
                  </span>
                </div>
              </div>
            )}

            {/* Value Display */}
            {isResolved ? (
              <div className="space-y-1">
                <div className="text-xs font-medium text-green-700 dark:text-green-300">
                  已解析值:
                </div>
                <div className="text-sm bg-background/70 p-3 rounded border max-h-32 overflow-y-auto">
                  <pre className="whitespace-pre-wrap break-words font-sans text-sm">
                    {placeholder.value}
                  </pre>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-yellow-500 animate-pulse" />
                <span className="text-xs text-yellow-700 dark:text-yellow-300 font-medium">
                  等待解析...
                </span>
              </div>
            )}

            {/* Data Flow Visualization */}
            {renderDataFlowVisualization(placeholder)}

            {/* Detailed Info (Expandable) */}
            <AnimatePresence>
              {showDetailView && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-3 space-y-2 text-xs"
                >
                  {placeholder.destinationAgents.length > 0 && (
                    <div>
                      <span className="font-medium">目标代理:</span> {placeholder.destinationAgents.join(', ')}
                    </div>
                  )}
                  {placeholder.dependsOn.length > 0 && (
                    <div>
                      <span className="font-medium">依赖变量:</span> {placeholder.dependsOn.join(', ')}
                    </div>
                  )}
                  {placeholder.contextDependencies.length > 0 && (
                    <div>
                      <span className="font-medium">上下文依赖:</span> {placeholder.contextDependencies.join(', ')}
                    </div>
                  )}
                  {placeholder.resolvedAt && (
                    <div>
                      <span className="font-medium">解析时间:</span> {new Date(placeholder.resolvedAt).toLocaleString('zh-CN')}
                    </div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Status & Actions */}
          <div className="flex flex-col items-center space-y-2">
            {isResolved ? (
              <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                <CheckCircle className="h-3 w-3 text-white" />
              </div>
            ) : (
              <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center animate-pulse">
                <Clock className="h-3 w-3 text-white" />
              </div>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => toggleDetails(placeholder.id)}
              className="h-6 w-6 p-0"
            >
              {showDetailView ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
            </Button>
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2 text-base">
          <Settings className="h-4 w-4 text-primary" />
          <span>增强变量跟踪</span>
          <Badge variant="secondary" className="text-xs">
            {variablePlaceholders.length} 个变量
          </Badge>
          <Badge
            variant={
              websocketStatus === 'connected' ? 'default' :
              websocketStatus === 'connecting' ? 'secondary' : 'outline'
            }
            className="text-xs"
          >
            {websocketStatus === 'connected' ? '🔗 实时连接' :
             websocketStatus === 'connecting' ? '🔄 连接中' : '📡 离线模式'}
          </Badge>
        </CardTitle>
        <CardDescription className="text-xs">
          实时跟踪代理间变量通信和数据流向，支持分类查看和详细分析
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        {variablePlaceholders.length > 0 ? (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-5 mb-4">
              <TabsTrigger value="all" className="text-xs">
                全部 ({groupedVariables.all.length})
              </TabsTrigger>
              <TabsTrigger value="inter-agent" className="text-xs">
                代理间 ({groupedVariables['inter-agent'].length})
              </TabsTrigger>
              <TabsTrigger value="user-input" className="text-xs">
                用户 ({groupedVariables['user-input'].length})
              </TabsTrigger>
              <TabsTrigger value="system" className="text-xs">
                系统 ({groupedVariables['system'].length})
              </TabsTrigger>
              <TabsTrigger value="internal" className="text-xs">
                内部 ({groupedVariables['internal'].length})
              </TabsTrigger>
            </TabsList>

            {Object.entries(groupedVariables).map(([type, variables]) => (
              <TabsContent key={type} value={type} className="mt-0">
                <div className="space-y-3">
                  <AnimatePresence>
                    {variables
                      .sort((a, b) => (a.stepIndex || 0) - (b.stepIndex || 0))
                      .map(renderVariableCard)}
                  </AnimatePresence>
                </div>
              </TabsContent>
            ))}
          </Tabs>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
              <Settings className="h-6 w-6 text-muted-foreground" />
            </div>
            <h3 className="text-sm font-medium mb-2">等待变量解析</h3>
            <p className="text-xs">
              {isExecuting
                ? "执行过程中发现的变量占位符将显示在这里"
                : "开始测试后，团队变量的解析过程将在此显示"
              }
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
