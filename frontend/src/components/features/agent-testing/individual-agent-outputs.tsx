"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { JsonFormatter } from "@/components/ui/json-formatter";
import { 
  <PERSON><PERSON>, 
  Copy, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  User,
  Cpu,
  Zap
} from "lucide-react";

interface IndividualAgentOutput {
  step_index: number;
  step_name: string;
  agent_name: string;
  agent_role: string;
  output: string;
  status: string;
  execution_method: string;
  ai_config_used: Record<string, any>;
  capabilities_used: string[];
  timestamp: string;
  description: string;
  error?: string;
  total_steps: number;
}

interface AgentExecutionSequence {
  sequence_order: number;
  step_name: string;
  agent_name: string;
  agent_role: string;
  start_time: string;
  status: string;
  execution_method: string;
  has_error: boolean;
}

interface IndividualAgentOutputsProps {
  individualAgentOutputs?: IndividualAgentOutput[];
  agentExecutionSequence?: AgentExecutionSequence[];
  className?: string;
}

export function IndividualAgentOutputs({ 
  individualAgentOutputs = [], 
  agentExecutionSequence = [],
  className = "" 
}: IndividualAgentOutputsProps) {
  const [selectedAgentTab, setSelectedAgentTab] = useState<string>("");

  // Group outputs by agent
  const agentGroups = individualAgentOutputs.reduce((groups, output) => {
    const agentKey = `${output.agent_name}-${output.agent_role}`;
    if (!groups[agentKey]) {
      groups[agentKey] = {
        agent_name: output.agent_name,
        agent_role: output.agent_role,
        outputs: []
      };
    }
    groups[agentKey].outputs.push(output);
    return groups;
  }, {} as Record<string, { agent_name: string; agent_role: string; outputs: IndividualAgentOutput[] }>);

  const agentTabs = Object.entries(agentGroups);

  // Set default tab if not set and we have agents
  if (!selectedAgentTab && agentTabs.length > 0) {
    setSelectedAgentTab(agentTabs[0][0]);
  }

  if (agentTabs.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bot className="h-5 w-5" />
            <span>个体Agent输出</span>
          </CardTitle>
          <CardDescription>
            每个团队成员的独立执行结果
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12 text-muted-foreground">
            <div className="text-4xl mb-4">🤖</div>
            <h3 className="text-lg font-medium mb-2">暂无个体Agent输出</h3>
            <p className="text-sm max-w-md mx-auto">
              当团队执行任务时，每个Agent的独立输出将在这里显示
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";
      case "error":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300";
      default:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300";
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Bot className="h-5 w-5" />
          <span>个体Agent输出</span>
          <Badge variant="outline" className="ml-2">
            {agentTabs.length} 个Agent
          </Badge>
        </CardTitle>
        <CardDescription>
          每个团队成员的独立执行结果，按Agent分组显示
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={selectedAgentTab} onValueChange={setSelectedAgentTab} className="w-full">
          <TabsList className="grid w-full grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-1 h-auto p-1">
            {agentTabs.map(([agentKey, agentData]) => (
              <TabsTrigger
                key={agentKey}
                value={agentKey}
                className="flex flex-col items-center space-y-1 p-3 h-auto data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4" />
                  <span className="font-medium text-sm truncate max-w-20">
                    {agentData.agent_name}
                  </span>
                </div>
                <span className="text-xs text-muted-foreground truncate max-w-24">
                  {agentData.agent_role}
                </span>
                <Badge variant="secondary" className="text-xs">
                  {agentData.outputs.length} 步骤
                </Badge>
              </TabsTrigger>
            ))}
          </TabsList>

          {agentTabs.map(([agentKey, agentData]) => (
            <TabsContent key={agentKey} value={agentKey} className="mt-6">
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-4"
              >
                {/* Agent Header */}
                <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg border">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                      <User className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold">{agentData.agent_name}</h3>
                      <p className="text-sm text-muted-foreground">{agentData.agent_role}</p>
                    </div>
                  </div>
                  <Badge variant="outline">
                    {agentData.outputs.length} 个输出
                  </Badge>
                </div>

                {/* Agent Outputs */}
                <div className="space-y-4">
                  {agentData.outputs
                    .sort((a, b) => a.step_index - b.step_index)
                    .map((output, index) => (
                      <motion.div
                        key={`${agentKey}-${output.step_index}`}
                        className="border rounded-lg p-4 bg-background hover:bg-muted/20 transition-colors"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                      >
                        {/* Step Header */}
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium">
                              {output.step_index + 1}
                            </div>
                            <div>
                              <h4 className="font-medium">{output.step_name}</h4>
                              {output.description && (
                                <p className="text-sm text-muted-foreground">{output.description}</p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${getStatusColor(output.status)}`}>
                              {getStatusIcon(output.status)}
                              <span>{output.status}</span>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                navigator.clipboard.writeText(output.output);
                              }}
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        {/* Step Output */}
                        <div className="bg-muted/30 rounded-lg p-3">
                          {output.output ? (
                            <JsonFormatter
                              content={output.output}
                              isStreaming={false}
                              className="text-sm"
                              maxHeight="max-h-64"
                            />
                          ) : (
                            <div className="text-center py-4 text-muted-foreground">
                              <p className="text-sm">此步骤暂无输出内容</p>
                            </div>
                          )}
                        </div>

                        {/* Step Metadata */}
                        <div className="mt-3 flex flex-wrap gap-2 text-xs text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <Clock className="h-3 w-3" />
                            <span>{new Date(output.timestamp).toLocaleString()}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Cpu className="h-3 w-3" />
                            <span>{output.execution_method}</span>
                          </div>
                          {output.ai_config_used?.model && (
                            <div className="flex items-center space-x-1">
                              <Zap className="h-3 w-3" />
                              <span>{output.ai_config_used.provider}/{output.ai_config_used.model}</span>
                            </div>
                          )}
                        </div>

                        {/* Error Display */}
                        {output.error && (
                          <div className="mt-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                            <div className="flex items-center space-x-2 text-red-700 dark:text-red-300">
                              <XCircle className="h-4 w-4" />
                              <span className="font-medium text-sm">执行错误</span>
                            </div>
                            <p className="text-sm text-red-600 dark:text-red-400 mt-1">{output.error}</p>
                          </div>
                        )}
                      </motion.div>
                    ))}
                </div>
              </motion.div>
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  );
}
