"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { LoadingButton } from "@/components/ui/loading";
import { StreamingMarkdownRenderer } from "@/components/ui/markdown-renderer";
import { JsonFormatter } from "@/components/ui/json-formatter";
import { Slider } from "@/components/ui/slider";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { api } from "@/lib/api";
import { type AIModelOverride as AIModelOverrideType } from "./ai-model-override";
import { TestCaseManager, type TestCase } from "./test-case-manager";
import { BatchTesting } from "./batch-testing";
import { UnifiedTestDetailDialog } from "../test-history/unified-test-detail-dialog";
import { IndividualAgentOutputs } from "./individual-agent-outputs";
import { EnhancedVariableTracking } from "./enhanced-variable-tracking";
import { useKeyboardShortcuts, createTestInterfaceShortcuts, useKeyboardShortcutsHelp } from "@/hooks/use-keyboard-shortcuts";
import { APIKey } from "@/lib/types";
import { getAgentWorkflowSteps } from "@/lib/utils";
import { createVariableTrackingWebSocket, type VariableUpdate, type VariableTrackingCallbacks } from "@/lib/websocket";
import {
  Bot,
  Play,
  Square,
  RotateCcw,
  Settings,
  History,
  Zap,
  ChevronDown,
  Users,
  Key,
  TestTube,
  AlertCircle,
  CheckCircle,
  Clock,
  DollarSign,
  Copy,
  ArrowRightLeft,
  ArrowRight,
  ArrowLeft,
  Share,
  Cpu,
  Globe,
  Timer,
  XCircle,
  Pause,
  RefreshCw,
  Eye
} from "lucide-react";
import { format } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";

interface Agent {
  agent_id: string;
  team_name: string;
  description: string;
  status: "active" | "inactive" | "error";
  api_endpoint: string;
  created_at?: string;
  team_plan?: {
    team_members: Array<{
      role: string;
      context_placeholders?: Array<{
        placeholder_name?: string;
        name?: string;
        source_step?: string;
        source_agent_role?: string;
        semantic_description?: string;
        description?: string;
      }>;
    }>;
  };
}

interface TestInterfaceProps {
  agent: Agent;
  agents: Agent[];
  onTestSubmit: (input: string, options: any, aiOverride?: AIModelOverrideType, responseMetadata?: any) => void;
  onAgentChange?: () => void;
  initialParams?: {
    input_text?: string | null;
    ai_config?: string | null;
    api_key_name?: string | null;
  };
}

const exampleInputs = [
  {
    title: "侦探案例",
    content: "城北数据中心的管理员失踪了，这是唯一的线索：一张写着'枯蝉'的纸条。",
    suitable: ["zen-rizzo-001"]
  },
  {
    title: "技术咨询",
    content: "我想开发一个电商网站，需要支持高并发，请给我技术架构建议。",
    suitable: ["tech-team-002"]
  },
  {
    title: "创意写作",
    content: "帮我创作一个科幻短篇小说，主题是人工智能与人类的关系。",
    suitable: ["creative-writers-003"]
  },
  {
    title: "通用问题",
    content: "请分析一下当前人工智能技术的发展趋势和挑战。",
    suitable: ["zen-rizzo-001", "tech-team-002", "creative-writers-003"]
  }
];

// Helper functions for history tab
const getHistoryStatusConfig = (status: string) => {
  const configs = {
    running: {
      icon: Zap,
      color: "text-blue-600 dark:text-blue-400",
      bgColor: "bg-blue-50 dark:bg-blue-950/50",
      borderColor: "border-blue-200 dark:border-blue-800",
      label: "运行中"
    },
    completed: {
      icon: CheckCircle,
      color: "text-green-600 dark:text-green-400",
      bgColor: "bg-green-50 dark:bg-green-950/50",
      borderColor: "border-green-200 dark:border-green-800",
      label: "已完成"
    },
    failed: {
      icon: XCircle,
      color: "text-red-600 dark:text-red-400",
      bgColor: "bg-red-50 dark:bg-red-950/50",
      borderColor: "border-red-200 dark:border-red-800",
      label: "失败"
    },
    cancelled: {
      icon: Pause,
      color: "text-muted-foreground",
      bgColor: "bg-muted/50",
      borderColor: "border-border",
      label: "已取消"
    }
  };
  return configs[status as keyof typeof configs] || configs.cancelled;
};

const formatHistoryDuration = (ms?: number) => {
  if (!ms) return "未知";
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  return `${(ms / 60000).toFixed(1)}min`;
};

const truncateHistoryText = (text: string, maxLength: number = 100) => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + "...";
};



export function TestInterface({ agent, agents, onAgentChange, initialParams }: TestInterfaceProps) {
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState<string | null>(null);
  const [responseMetadata, setResponseMetadata] = useState<any>(null);
  const [apiKeys, setApiKeys] = useState<APIKey[]>([]);
  const [loadingApiKeys, setLoadingApiKeys] = useState(true);
  const [aiOverride, setAiOverride] = useState<AIModelOverrideType>({
    enabled: false,
    provider: "openai",
    model: "",
    temperature: 0.7,
    maxTokens: 2000,
    baseUrl: undefined,
    customProviderName: undefined,
    apiKeyId: undefined
  });
  const [options, setOptions] = useState({
    stream: true // Default to streaming for progress updates
  });

  // Progress tracking state
  const [progress, setProgress] = useState({
    stage: "",
    message: "",
    progress: 0,
    currentStep: 0,
    totalSteps: 0,
    stepName: "",
    assignee: ""
  });
  const [showProgress, setShowProgress] = useState(false);

  // Real-time response tracking
  const [realtimeResponse, setRealtimeResponse] = useState<string>("");

  // Progress updates for test history
  const [progressUpdates, setProgressUpdates] = useState<any[]>([]);

  // Individual agent outputs tracking
  const [individualAgentOutputs, setIndividualAgentOutputs] = useState<any[]>([]);
  const [agentExecutionSequence, setAgentExecutionSequence] = useState<any[]>([]);

  // Enhanced variable placeholder tracking with inter-agent communication
  const [variablePlaceholders, setVariablePlaceholders] = useState<Array<{
    id: string;
    placeholderName: string;
    sourceStep: string;
    sourceAgent: string;
    semanticDescription: string;
    value: string | null;
    resolvedAt: string | null;
    stepIndex: number;
    // Inter-agent communication fields
    communicationType: 'inter-agent' | 'user-input' | 'system' | 'internal';
    destinationAgents: string[];
    dependsOn: string[];
    dataFlowChain: Array<{
      agent: string;
      step: string;
      timestamp: string;
    }>;
    contextDependencies: string[];
    isSharedBetweenAgents: boolean;
  }>>([]);

  // Enhanced variable tracking with API discovery and WebSocket
  const [discoveredVariables, setDiscoveredVariables] = useState<any[]>([]);
  const [variableDiscoveryLoading, setVariableDiscoveryLoading] = useState(false);
  const [websocketConnection, setWebsocketConnection] = useState<any>(null);
  const [websocketStatus, setWebsocketStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const [forceRender, setForceRender] = useState(0); // Force re-render trigger

  // Enhanced tabbed execution mode
  const [mainActiveTab, setMainActiveTab] = useState<string>("test");
  const [executionTabs, setExecutionTabs] = useState<Array<{
    id: string;
    name: string;
    icon: string;
    enabled: boolean;
    completed: boolean;
    content: string;
    type: 'overview' | 'workflow' | 'results';
  }>>([]);

  // Multi-tab response tracking (legacy support)
  const [stageResponses, setStageResponses] = useState<Record<string, string>>({});
  const [activeResponseTab, setActiveResponseTab] = useState<string>("overview");
  const [availableTabs, setAvailableTabs] = useState<string[]>(["overview"]);

  // Workflow-based tab management
  const [workflowSteps, setWorkflowSteps] = useState<any[]>([]);
  const [useWorkflowTabs, setUseWorkflowTabs] = useState<boolean>(false);

  // Execution state tracking
  const [executionState, setExecutionState] = useState<{
    isRunning: boolean;
    currentStage: string;
    currentStep: string;
    completedSteps: string[];
    hasResults: boolean;
  }>({
    isRunning: false,
    currentStage: '',
    currentStep: '',
    completedSteps: [],
    hasResults: false
  });

  // Recent test history for current agent
  const [recentTests, setRecentTests] = useState<any[]>([]);
  const [loadingRecentTests, setLoadingRecentTests] = useState(false);

  // Execution stages for step-by-step results display
  const [executionStages, setExecutionStages] = useState<any[]>([]);

  // Test detail dialog state
  const [showDetailDialog, setShowDetailDialog] = useState(false);
  const [selectedTest, setSelectedTest] = useState<any>(null);

  // Load API keys on component mount
  useEffect(() => {
    const loadApiKeys = async () => {
      try {
        setLoadingApiKeys(true);
        const response = await api.apiKeys.list();
        if (response.success && response.data) {
          // Handle both possible response formats
          const apiKeys = response.data.api_keys || response.data;
          setApiKeys(Array.isArray(apiKeys) ? apiKeys : []);
          console.log('Loaded API keys:', apiKeys);
        } else {
          console.warn('Failed to load API keys:', response);
          setApiKeys([]);
        }
      } catch (error) {
        console.error('Failed to load API keys:', error);
        setApiKeys([]);
      } finally {
        setLoadingApiKeys(false);
      }
    };

    loadApiKeys();
    loadRecentTests();
    initializeWorkflowTabs();
  }, [agent.agent_id]);

  // Handle initial parameters from URL (for rerun functionality)
  useEffect(() => {
    if (initialParams) {
      console.log('Processing initial parameters:', initialParams);

      // Set input text if provided
      if (initialParams.input_text) {
        setInput(initialParams.input_text);
        console.log('Set initial input:', initialParams.input_text);
      }

      // Set AI configuration if provided
      if (initialParams.ai_config) {
        try {
          const aiConfig = JSON.parse(initialParams.ai_config);
          console.log('Parsed AI config:', aiConfig);
          setAiOverride(prev => ({
            ...prev,
            enabled: true,
            provider: aiConfig.provider || prev.provider,
            model: aiConfig.model || prev.model,
            temperature: aiConfig.temperature ?? prev.temperature,
            maxTokens: aiConfig.max_tokens ?? prev.maxTokens,
            baseUrl: aiConfig.base_url || prev.baseUrl,
            customProviderName: aiConfig.custom_provider_name || prev.customProviderName,
            apiKeyId: aiConfig.api_key_id || prev.apiKeyId
          }));
        } catch (error) {
          console.error("Failed to parse AI config from URL:", error);
        }
      }

      // Set API key if provided (will be handled after API keys are loaded)
      if (initialParams.api_key_name && apiKeys.length > 0) {
        const matchingKey = apiKeys.find(key => key.name === initialParams.api_key_name);
        if (matchingKey) {
          console.log('Found matching API key:', matchingKey.name);
          setAiOverride(prev => ({
            ...prev,
            apiKeyId: matchingKey.id
          }));
        } else {
          console.warn('API key not found:', initialParams.api_key_name);
        }
      }
    }
  }, [initialParams, apiKeys]);

  // Helper function to get the display text for the selected API key
  const getApiKeyDisplayText = () => {
    if (loadingApiKeys) {
      return "加载中...";
    }

    if (!aiOverride.apiKeyId || aiOverride.apiKeyId === "default") {
      return "使用默认密钥";
    }

    // Handle both string and number ID types by normalizing to string for comparison
    const searchId = String(aiOverride.apiKeyId);
    const selectedKey = apiKeys.find(key => String(key.id) === searchId);

    if (selectedKey) {
      return selectedKey.name;
    }

    // Fallback if key not found - only warn if we have API keys loaded and still can't find it
    if (apiKeys.length > 0) {
      console.warn('Selected API key not found:', aiOverride.apiKeyId, 'Available keys:', apiKeys.map(k => k.id));
    }
    return "使用默认密钥";
  };

  // Auto-switch to test tab when agent is selected (improved workflow)
  useEffect(() => {
    // Always start with the test tab when an agent is selected
    setMainActiveTab("test");

    // Reset execution state when switching agents
    setExecutionState({
      isRunning: false,
      currentStage: '',
      currentStep: '',
      completedSteps: [],
      hasResults: false
    });
  }, [agent.agent_id]);





  // Initialize workflow tabs based on agent's workflow steps
  const initializeWorkflowTabs = () => {
    const steps = getAgentWorkflowSteps(agent);
    setWorkflowSteps(steps);

    if (steps.length > 0) {
      setUseWorkflowTabs(true);
      // Initialize tabs with overview + workflow steps
      const workflowTabNames = steps.map(step => step.name);
      setAvailableTabs(["overview", ...workflowTabNames]);
    } else {
      setUseWorkflowTabs(false);
      setAvailableTabs(["overview"]);
    }

    // Initialize execution tabs
    initializeExecutionTabs(steps);

    // Initialize variable placeholders from agent team configuration
    if (agent.team_plan && agent.team_plan.team_members) {
      const placeholders: Array<{
        id: string;
        placeholderName: string;
        sourceStep: string;
        sourceAgent: string;
        semanticDescription: string;
        value: string | null;
        resolvedAt: string | null;
        stepIndex: number;
        communicationType: 'inter-agent' | 'user-input' | 'system' | 'internal';
        destinationAgents: string[];
        dependsOn: string[];
        dataFlowChain: Array<{
          agent: string;
          step: string;
          timestamp: string;
        }>;
        contextDependencies: string[];
        isSharedBetweenAgents: boolean;
      }> = [];

      agent.team_plan.team_members.forEach((member: any, memberIndex: number) => {
        if (member.context_placeholders && Array.isArray(member.context_placeholders)) {
          member.context_placeholders.forEach((placeholder: any, placeholderIndex: number) => {
            placeholders.push({
              id: `init-${memberIndex}-${placeholderIndex}`,
              placeholderName: placeholder.placeholder_name || `{${placeholder.name || 'unknown'}}`,
              sourceStep: placeholder.source_step || 'unknown',
              sourceAgent: placeholder.source_agent_role || member.role || 'unknown',
              semanticDescription: placeholder.semantic_description || placeholder.description || '',
              value: null,
              resolvedAt: null,
              stepIndex: memberIndex,
              communicationType: 'internal',
              destinationAgents: [],
              dependsOn: [],
              dataFlowChain: [],
              contextDependencies: [],
              isSharedBetweenAgents: false
            });
          });
        }
      });

      setVariablePlaceholders(placeholders);
    }
  };

  // Initialize execution tabs for the enhanced tabbed interface
  const initializeExecutionTabs = (steps: any[]) => {
    const tabs: Array<{
      id: string;
      name: string;
      icon: string;
      enabled: boolean;
      completed: boolean;
      content: string;
      type: 'overview' | 'workflow' | 'results';
    }> = [
      {
        id: "overview",
        name: "总览",
        icon: "📊",
        enabled: true,
        completed: false,
        content: "",
        type: 'overview'
      }
    ];

    // Add workflow step tabs
    steps.forEach((step) => {
      tabs.push({
        id: step.name,
        name: step.name,
        icon: "⚡",
        enabled: false,
        completed: false,
        content: "",
        type: 'workflow'
      });
    });

    // Add results tab
    tabs.push({
      id: "results",
      name: "结果",
      icon: "✅",
      enabled: false,
      completed: false,
      content: "",
      type: 'results'
    });

    setExecutionTabs(tabs);
  };

  // Enhanced useEffect hooks for variable discovery and WebSocket integration
  useEffect(() => {
    if (agent?.agent_id) {
      console.log("🔍 [VARIABLE DISCOVERY] Agent changed, discovering variables for:", agent.agent_id);
      discoverAgentVariables(agent.agent_id);
    }
  }, [agent?.agent_id]);

  useEffect(() => {
    return () => {
      disconnectWebSocket();
    };
  }, [agent?.agent_id]);

  // Monitor WebSocket connection status and force re-render when needed
  useEffect(() => {
    console.log("🔌 [WebSocket] Status changed to:", websocketStatus);
    // Force re-render when WebSocket status changes to ensure UI updates
    setForceRender(prev => prev + 1);
  }, [websocketStatus]);

  // Update execution tab content and state
  const updateExecutionTab = (tabId: string, content: string, enabled?: boolean, completed?: boolean) => {
    setExecutionTabs(prev => prev.map(tab => {
      if (tab.id === tabId) {
        return {
          ...tab,
          content: content,
          enabled: enabled !== undefined ? enabled : tab.enabled,
          completed: completed !== undefined ? completed : tab.completed
        };
      }
      return tab;
    }));
  };

  // Enable execution tab and optionally switch to it
  const enableExecutionTab = (tabId: string, switchTo: boolean = true) => {
    setExecutionTabs(prev => prev.map(tab => {
      if (tab.id === tabId) {
        return { ...tab, enabled: true };
      }
      return tab;
    }));

    if (switchTo && mainActiveTab === "execution") {
      setActiveResponseTab(tabId);
    }
  };

  // Mark execution tab as completed
  const completeExecutionTab = (tabId: string) => {
    setExecutionTabs(prev => prev.map(tab => {
      if (tab.id === tabId) {
        return { ...tab, completed: true };
      }
      return tab;
    }));

    // Update execution state
    setExecutionState(prev => ({
      ...prev,
      completedSteps: [...prev.completedSteps.filter(s => s !== tabId), tabId]
    }));
  };

  // Helper function to calculate correct step index based on workflow position
  const calculateStepIndex = (assignee: string, stepName: string, fallbackIndex: number = 0): number => {
    // First try to find by assignee
    const stepByAssignee = workflowSteps.findIndex(step =>
      step.assignee === assignee ||
      step.assignee.toLowerCase() === assignee.toLowerCase()
    );

    if (stepByAssignee >= 0) {
      console.log("🔍 [STEP INDEX] Found step by assignee:", { assignee, stepIndex: stepByAssignee });
      return stepByAssignee;
    }

    // Then try to find by step name
    const stepByName = workflowSteps.findIndex(step =>
      step.name === stepName ||
      step.name.toLowerCase() === stepName.toLowerCase()
    );

    if (stepByName >= 0) {
      console.log("🔍 [STEP INDEX] Found step by name:", { stepName, stepIndex: stepByName });
      return stepByName;
    }

    // Special handling for system/user variables
    if (assignee === 'user' || assignee === 'system') {
      console.log("🔍 [STEP INDEX] Using index 0 for system/user variable");
      return 0;
    }

    console.log("🔍 [STEP INDEX] Using fallback index:", { assignee, stepName, fallbackIndex });
    return fallbackIndex;
  };

  // Enhanced function to update variable placeholders with inter-agent communication tracking
  const updateVariablePlaceholder = (
    placeholderName: string,
    value: string,
    sourceStep: string,
    sourceAgent: string,
    semanticDescription: string = "",
    stepIndex: number = 0,
    communicationType: 'inter-agent' | 'user-input' | 'system' | 'internal' = 'internal',
    destinationAgents: string[] = [],
    dependsOn: string[] = [],
    contextDependencies: string[] = []
  ) => {
    const timestamp = new Date().toISOString();

    // Ensure placeholder name is in proper format with braces
    const normalizedPlaceholderName = placeholderName.startsWith('{') && placeholderName.endsWith('}')
      ? placeholderName
      : `{${placeholderName}}`;

    // Calculate correct step index based on workflow position
    const correctedStepIndex = calculateStepIndex(sourceAgent, sourceStep, stepIndex);

    const placeholderId = `${normalizedPlaceholderName}-${sourceStep}-${Date.now()}`;

    console.log("🔍 [VARIABLE DEBUG] updateVariablePlaceholder called:", {
      originalName: placeholderName,
      normalizedName: normalizedPlaceholderName,
      value: value.substring(0, 50) + (value.length > 50 ? "..." : ""),
      sourceStep,
      sourceAgent,
      originalStepIndex: stepIndex,
      correctedStepIndex,
      communicationType,
      destinationAgents,
      dependsOn
    });

    setVariablePlaceholders(prev => {
      // Check if placeholder already exists (try both original and normalized names)
      const existingIndex = prev.findIndex(p =>
        (p.placeholderName === normalizedPlaceholderName || p.placeholderName === placeholderName) &&
        p.sourceStep === sourceStep
      );

      if (existingIndex >= 0) {
        // Update existing placeholder with enhanced inter-agent tracking
        const updated = [...prev];
        const existing = updated[existingIndex];

        // Update data flow chain
        const newDataFlowEntry = {
          agent: sourceAgent,
          step: sourceStep,
          timestamp
        };

        updated[existingIndex] = {
          ...existing,
          value,
          resolvedAt: timestamp,
          stepIndex: correctedStepIndex,
          communicationType,
          destinationAgents: [...new Set([...existing.destinationAgents, ...destinationAgents])],
          dependsOn: [...new Set([...existing.dependsOn, ...dependsOn])],
          dataFlowChain: [...existing.dataFlowChain, newDataFlowEntry],
          contextDependencies: [...new Set([...existing.contextDependencies, ...contextDependencies])],
          isSharedBetweenAgents: communicationType === 'inter-agent' || destinationAgents.length > 0
        };
        return updated;
      } else {
        // Add new placeholder with inter-agent communication tracking
        const isSharedBetweenAgents = communicationType === 'inter-agent' || destinationAgents.length > 0;

        return [...prev, {
          id: placeholderId,
          placeholderName: normalizedPlaceholderName,
          sourceStep,
          sourceAgent,
          semanticDescription,
          value,
          resolvedAt: timestamp,
          stepIndex: correctedStepIndex,
          communicationType,
          destinationAgents: [...destinationAgents],
          dependsOn: [...dependsOn],
          dataFlowChain: [{
            agent: sourceAgent,
            step: sourceStep,
            timestamp
          }],
          contextDependencies: [...contextDependencies],
          isSharedBetweenAgents
        }];
      }
    });
  };

  // Helper function to detect inter-agent communication patterns
  const detectInterAgentCommunication = (
    placeholderName: string,
    sourceAgent: string,
    sourceStep: string,
    progressData: any
  ) => {
    const communicationInfo = {
      communicationType: 'internal' as 'inter-agent' | 'user-input' | 'system' | 'internal',
      destinationAgents: [] as string[],
      dependsOn: [] as string[],
      contextDependencies: [] as string[]
    };

    // Detect user input variables
    if (sourceAgent === 'user' || sourceStep === 'user_input' || placeholderName.includes('user.')) {
      communicationInfo.communicationType = 'user-input';
      // User input is typically used by the first agent in the workflow
      if (workflowSteps.length > 0) {
        communicationInfo.destinationAgents = [workflowSteps[0].assignee];
      }
    }
    // Detect system variables
    else if (sourceAgent === 'system' || placeholderName.includes('system.')) {
      communicationInfo.communicationType = 'system';
    }
    // Detect inter-agent communication
    else if (placeholderName.includes('.') && !placeholderName.includes('user.') && !placeholderName.includes('system.')) {
      communicationInfo.communicationType = 'inter-agent';

      // Extract source agent from semantic placeholder name
      const match = placeholderName.match(/\{([^.]+)\./);
      if (match) {
        // Find potential destination agents (agents that come after this one in workflow)
        const currentStepIndex = workflowSteps.findIndex(step =>
          step.assignee === sourceAgent || step.name === sourceStep
        );

        if (currentStepIndex >= 0 && currentStepIndex < workflowSteps.length - 1) {
          // Add subsequent agents as potential destinations
          for (let i = currentStepIndex + 1; i < workflowSteps.length; i++) {
            communicationInfo.destinationAgents.push(workflowSteps[i].assignee);
          }
        }

        // Check for context dependencies
        if (progressData.context_dependencies) {
          communicationInfo.contextDependencies = progressData.context_dependencies;
        }
      }
    }

    // Check for dependencies from progress data
    if (progressData.depends_on) {
      communicationInfo.dependsOn = Array.isArray(progressData.depends_on)
        ? progressData.depends_on
        : [progressData.depends_on];
    }

    return communicationInfo;
  };

  // Discover variables from agent team configuration
  const discoverAgentVariables = async (agentId: string) => {
    if (!agentId) return;

    setVariableDiscoveryLoading(true);
    console.log("🔍 [VARIABLE DISCOVERY] Starting discovery for agent:", agentId);

    try {
      const response = await api.agents.discoverVariables(agentId);

      if (response.success && response.data) {
        const variables = response.data.variables;
        console.log("🔍 [VARIABLE DISCOVERY] Discovered variables:", variables);

        setDiscoveredVariables(variables);

        console.log("🔍 [VARIABLE DISCOVERY] Raw variables data:", variables);
        console.log("🔍 [VARIABLE DISCOVERY] Variables count:", variables.length);

        // Initialize variable placeholders with discovered variables
        const initialPlaceholders = variables.map((variable, index) => {
          console.log(`🔍 [VARIABLE DISCOVERY] Processing variable ${index}:`, variable);

          if (!variable) {
            console.warn(`🔍 [VARIABLE DISCOVERY] Variable ${index} is null/undefined`);
            return null;
          }

          if (!variable.placeholder) {
            console.warn(`🔍 [VARIABLE DISCOVERY] Variable ${index} missing placeholder:`, variable);
            // 如果没有placeholder，尝试生成一个
            if (variable.name) {
              variable.placeholder = `{${variable.name}}`;
              console.log(`🔍 [VARIABLE DISCOVERY] Generated placeholder for variable ${index}: ${variable.placeholder}`);
            } else {
              console.error(`🔍 [VARIABLE DISCOVERY] Variable ${index} has no name or placeholder, skipping`);
              return null;
            }
          }

          try {
            // Calculate proper step index based on workflow position
            let stepIndex = 0;
            if (typeof variable.workflow_step === 'number') {
              stepIndex = variable.workflow_step;
            } else {
              // Fallback: use index as step position
              stepIndex = index;
            }

            const communicationType: 'inter-agent' | 'user-input' | 'system' | 'internal' =
              variable.variable_type === 'inter-agent' ? 'inter-agent' :
              variable.variable_type === 'user-input' ? 'user-input' :
              variable.variable_type === 'system' ? 'system' : 'internal';

            const placeholder = {
              id: `discovered_${index}`,
              placeholderName: variable.placeholder || `{unknown_${index}}`,
              sourceStep: variable.variable_type || 'unknown',
              sourceAgent: variable.source_agent || "system",
              semanticDescription: variable.semantic_description || `Variable ${index}`,
              value: null as string | null, // Will be filled during execution
              resolvedAt: null as string | null,
              stepIndex: stepIndex,
              communicationType,
              destinationAgents: Array.isArray(variable.destination_agents) ? variable.destination_agents : [],
              dependsOn: Array.isArray(variable.dependencies) ? variable.dependencies : [],
              dataFlowChain: [] as Array<{
                agent: string;
                step: string;
                timestamp: string;
              }>,
              contextDependencies: [],
              isSharedBetweenAgents: variable.variable_type === 'inter-agent'
            };

            console.log(`🔍 [VARIABLE DISCOVERY] Created placeholder ${index}:`, placeholder);
            return placeholder;
          } catch (error) {
            console.error(`🔍 [VARIABLE DISCOVERY] Error creating placeholder ${index}:`, error, variable);
            return null;
          }
        }).filter((item): item is NonNullable<typeof item> => item !== null); // Remove null/undefined entries with type guard

        console.log("🔍 [VARIABLE DISCOVERY] Final placeholders array:", initialPlaceholders);
        console.log("🔍 [VARIABLE DISCOVERY] Final placeholders count:", initialPlaceholders.length);

        // Set the variable placeholders state with proper state update
        setVariablePlaceholders(initialPlaceholders);
        setForceRender(prev => prev + 1);
        setVariableDiscoveryLoading(false);

        console.log("🔍 [VARIABLE DISCOVERY] State update initiated, expected length:", initialPlaceholders.length);

        // Additional verification after state update
        setTimeout(() => {
          console.log("🔍 [VARIABLE DISCOVERY] Post-update verification completed");
        }, 100);
      } else {
        console.warn("🔍 [VARIABLE DISCOVERY] Failed to discover variables:", response.error);
        setVariablePlaceholders([]); // Clear any existing placeholders
        setForceRender(prev => prev + 1);
      }
    } catch (error) {
      console.error("🔍 [VARIABLE DISCOVERY] Error:", error);
      setVariablePlaceholders([]); // Clear any existing placeholders on error
      setForceRender(prev => prev + 1);
    } finally {
      setVariableDiscoveryLoading(false);
    }
  };

  // Establish WebSocket connection for real-time variable tracking
  const establishWebSocketConnection = async (agentId: string) => {
    if (!agentId) return;

    // Get auth token
    const token = localStorage.getItem('access_token');
    if (!token) {
      console.warn("🔌 [WebSocket] No auth token available");
      setWebsocketStatus('disconnected');
      return;
    }

    console.log("🔌 [WebSocket] Establishing connection for agent:", agentId);
    setWebsocketStatus('connecting');

    const callbacks: VariableTrackingCallbacks = {
      onConnectionEstablished: (sessionId) => {
        console.log("🔌 [WebSocket] Connection established, session:", sessionId);
        setWebsocketStatus('connected');
        // Force re-render to update UI
        setForceRender(prev => prev + 1);
      },

      onVariableUpdate: (update: VariableUpdate) => {
        console.log("🔌 [WebSocket] Variable update received:", update);

        // Update variable placeholders with real-time data
        setVariablePlaceholders(prev => {
          const updated = [...prev];

          // Ensure variable name is in proper placeholder format
          const variableName = update.variable_name;
          const placeholderName = variableName.startsWith('{') && variableName.endsWith('}')
            ? variableName
            : `{${variableName}}`;

          // Calculate correct step index using the helper function
          const correctedStepIndex = calculateStepIndex(
            update.source_agent,
            update.variable_type,
            update.execution_step || 0
          );

          console.log("🔌 [WebSocket] Processing variable update:", {
            originalName: variableName,
            placeholderName,
            sourceAgent: update.source_agent,
            originalStepIndex: update.execution_step,
            correctedStepIndex,
            variableType: update.variable_type
          });

          // Try to find existing variable by placeholder name (with or without braces)
          const existingIndex = updated.findIndex(v =>
            v.placeholderName === placeholderName ||
            v.placeholderName === variableName ||
            v.placeholderName === `{${variableName}}`
          );

          if (existingIndex >= 0) {
            // Update existing variable with proper step index
            updated[existingIndex] = {
              ...updated[existingIndex],
              value: update.variable_value,
              resolvedAt: update.timestamp,
              sourceAgent: update.source_agent,
              stepIndex: correctedStepIndex,
              placeholderName: placeholderName // Ensure consistent format
            };
            console.log("🔌 [WebSocket] Updated existing variable:", updated[existingIndex]);
          } else {
            // Add new variable discovered during execution
            const newVariable = {
              id: `ws_${Date.now()}`,
              placeholderName: placeholderName,
              sourceStep: update.variable_type,
              sourceAgent: update.source_agent,
              semanticDescription: update.metadata?.semantic_name
                ? `${update.source_agent}的输出结果`
                : `Real-time variable from ${update.source_agent}`,
              value: update.variable_value,
              resolvedAt: update.timestamp,
              stepIndex: correctedStepIndex,
              communicationType: update.variable_type as any,
              destinationAgents: update.destination_agents,
              dependsOn: [],
              dataFlowChain: [{
                agent: update.source_agent,
                step: update.variable_type,
                timestamp: update.timestamp
              }],
              contextDependencies: [],
              isSharedBetweenAgents: update.variable_type === 'inter-agent'
            };
            updated.push(newVariable);
            console.log("🔌 [WebSocket] Added new variable:", newVariable);
          }

          return updated;
        });

        // Force re-render after variable update
        setForceRender(prev => prev + 1);
      },

      onExecutionProgress: (progress) => {
        console.log("🔌 [WebSocket] Execution progress:", progress);
      },

      onError: (error) => {
        console.error("🔌 [WebSocket] Error:", error);
        setWebsocketStatus('disconnected');
        // Force re-render to update UI
        setForceRender(prev => prev + 1);
      },

      onDisconnect: () => {
        console.log("🔌 [WebSocket] Disconnected");
        setWebsocketStatus('disconnected');
        // Force re-render to update UI
        setForceRender(prev => prev + 1);
      }
    };

    try {
      const ws = createVariableTrackingWebSocket(agentId, token, callbacks);
      const connected = await ws.connect();

      if (connected) {
        setWebsocketConnection(ws);
        console.log("🔌 [WebSocket] Successfully connected");
        // Status will be set to 'connected' by onConnectionEstablished callback
      } else {
        console.error("🔌 [WebSocket] Failed to connect");
        setWebsocketStatus('disconnected');
        setForceRender(prev => prev + 1);
      }
    } catch (error) {
      console.error("🔌 [WebSocket] Connection error:", error);
      setWebsocketStatus('disconnected');
      setForceRender(prev => prev + 1);
    }
  };

  // Disconnect WebSocket
  const disconnectWebSocket = () => {
    if (websocketConnection) {
      websocketConnection.disconnect();
      setWebsocketConnection(null);
      setWebsocketStatus('disconnected');
      // Force re-render to update UI
      setForceRender(prev => prev + 1);
      console.log("🔌 [WebSocket] Manually disconnected");
    }
  };

  const loadRecentTests = async () => {
    try {
      setLoadingRecentTests(true);
      const response = await api.testHistory.getByAgent(agent.agent_id, { limit: 5, page: 1 });
      console.log("Recent tests response:", response);

      // Handle different response formats
      const tests = (response as any).tests || response.data?.tests || [];
      setRecentTests(tests);
    } catch (error) {
      console.error('Failed to load recent tests:', error);
      setRecentTests([]);
    } finally {
      setLoadingRecentTests(false);
    }
  };

  const loadTestDetail = async (testId: string) => {
    try {
      console.log("Loading test detail for:", testId);
      const response = await api.testHistory.getDetail(testId);
      console.log("Test detail response:", response);

      const detail = response.data || response;
      console.log("Processed detail:", detail);

      // Load individual agent outputs if available
      if (detail.individual_agent_outputs && Array.isArray(detail.individual_agent_outputs)) {
        setIndividualAgentOutputs(detail.individual_agent_outputs);
        console.log("Loaded individual agent outputs:", detail.individual_agent_outputs);
      }

      // Load agent execution sequence if available
      if (detail.agent_execution_sequence && Array.isArray(detail.agent_execution_sequence)) {
        setAgentExecutionSequence(detail.agent_execution_sequence);
        console.log("Loaded agent execution sequence:", detail.agent_execution_sequence);
      }

      // Load execution stages for backward compatibility
      if (detail.execution_stages && Array.isArray(detail.execution_stages)) {
        setExecutionStages(detail.execution_stages);
      }

      setSelectedTest(detail);
      setShowDetailDialog(true);
    } catch (error) {
      console.error("Failed to load test detail:", error);
      alert(`获取测试详情失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  };

  const handleLoadTestCase = (testCase: TestCase) => {
    setInput(testCase.input);
    if (testCase.aiOverride) {
      setAiOverride(testCase.aiOverride);
    }
  };

  const handleRunTestCase = async (testCase: TestCase) => {
    setInput(testCase.input);
    if (testCase.aiOverride) {
      setAiOverride(testCase.aiOverride);
    }
    // Wait a bit for state to update, then run the test
    setTimeout(() => {
      handleSubmit();
    }, 100);
  };

  const handleClearInput = () => {
    setInput("");
    setResponse(null);
    setRealtimeResponse("");
    setResponseMetadata(null);
    setShowProgress(false);
    setProgressUpdates([]);
    setExecutionStages([]);

    // Clear multi-tab responses and reset tabs based on workflow
    setStageResponses({});
    setActiveResponseTab("overview");

    // Reset tabs based on workflow steps
    if (useWorkflowTabs && workflowSteps.length > 0) {
      const workflowTabNames = workflowSteps.map(step => step.name);
      setAvailableTabs(["overview", ...workflowTabNames]);
    } else {
      setAvailableTabs(["overview"]);
    }

    // Reset execution state and tabs
    setMainActiveTab("test");
    setExecutionState({
      isRunning: false,
      currentStage: '',
      currentStep: '',
      completedSteps: [],
      hasResults: false
    });

    // Reset execution tabs
    initializeExecutionTabs(workflowSteps);
  };



  // Set up keyboard shortcuts
  useKeyboardShortcutsHelp();
  const shortcuts = createTestInterfaceShortcuts({
    onSubmit: () => !isLoading && input.trim() && handleSubmit(),
    onClear: handleClearInput,
    onSave: () => {}, // Will be implemented with test case manager
    onExport: () => {}, // Will be implemented with export functionality
    onToggleOverride: () => {} // Advanced config is now always accessible when expanded
  });
  useKeyboardShortcuts(shortcuts, true);



  const handleSubmit = async () => {
    console.log("handleSubmit called, isLoading:", isLoading, "input:", input.trim());
    if (!input.trim() || isLoading) {
      console.log("handleSubmit blocked - no input or already loading");
      return;
    }

    // Establish WebSocket connection for real-time variable tracking
    if (agent?.agent_id && websocketStatus === 'disconnected') {
      console.log("🔌 [WebSocket] Establishing connection before execution");
      await establishWebSocketConnection(agent.agent_id);
    }

    // Validate AI configuration when custom values are provided
    if (aiOverride.enabled || aiOverride.model.trim().length > 0 || aiOverride.baseUrl || aiOverride.apiKeyId) {
      // Validate temperature range
      if (aiOverride.temperature < 0 || aiOverride.temperature > 2) {
        alert("温度参数必须在0到2之间");
        return;
      }

      // Validate max tokens
      if (aiOverride.maxTokens < 1 || aiOverride.maxTokens > 32000) {
        alert("最大令牌数必须在1到32000之间");
        return;
      }

      // Validate base URL format if provided
      if (aiOverride.baseUrl && aiOverride.baseUrl.trim().length > 0) {
        try {
          new URL(aiOverride.baseUrl);
        } catch {
          alert("请输入有效的API端点URL");
          return;
        }
      }

      // API key is optional - if not provided, will use default
      if (aiOverride.apiKeyId && aiOverride.apiKeyId !== "default" && !apiKeys.find(key => String(key.id) === String(aiOverride.apiKeyId))) {
        alert("请选择一个有效的AI密钥");
        return;
      }
    }

    setIsLoading(true);
    // Clear previous responses to start fresh
    setResponse(null);
    setRealtimeResponse("");
    setResponseMetadata(null);
    setExecutionStages([]);
    setVariablePlaceholders([]); // Clear variable placeholders
    setShowProgress(true);

    // Initialize execution state
    setExecutionState({
      isRunning: true,
      currentStage: 'initializing',
      currentStep: '',
      completedSteps: [],
      hasResults: false
    });

    // Immediately create initial variables for demo purposes with inter-agent communication
    console.log("🔍 [VARIABLE DEBUG] Creating initial variables at execution start");
    setTimeout(() => {
      // Determine destination agents for user input
      const destinationAgents = workflowSteps.length > 0 ? [workflowSteps[0].assignee] : [];

      updateVariablePlaceholder(
        "{user.requirements}",
        input.trim(),
        "user_input",
        "user",
        "用户输入的需求和任务描述",
        0,
        "user-input",
        destinationAgents,
        [],
        []
      );

      updateVariablePlaceholder(
        "{system.execution_id}",
        `exec_${Date.now()}`,
        "system_init",
        "system",
        "系统生成的执行标识符",
        0,
        "system",
        [], // System variables typically not passed to specific agents
        [],
        []
      );
    }, 500); // Small delay to ensure state is set

    // Switch to execution tab and enable overview with smooth transition
    setTimeout(() => {
      setMainActiveTab("execution");
    }, 100); // Small delay for smooth transition
    enableExecutionTab("overview", true);
    updateExecutionTab("overview", "🔗 正在连接到代理...\n\n", true, false);

    try {
      // Use the real agent execution API
      await executeAgent();
      // Note: onTestSubmit callback removed to prevent infinite loops
      // The test completion is now handled internally
    } catch (error) {
      console.error("Agent execution error:", error);
      const timestamp = new Date().toLocaleTimeString();
      const errorMessage = `\n💥 [${timestamp}] 执行错误: ${error instanceof Error ? error.message : "测试失败，请稍后重试。"}\n`;

      // Update overview tab with error
      updateExecutionTab("overview", errorMessage, true, false);

      // Also update legacy response for compatibility
      setRealtimeResponse(prev => prev + errorMessage);
      setResponseMetadata({
        error: true,
        message: error instanceof Error ? error.message : "Unknown error"
      });

      // Update execution state
      setExecutionState(prev => ({
        ...prev,
        isRunning: false,
        currentStage: 'error'
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const executeAgent = async (): Promise<void> => {
    const startTime = Date.now();

    // Prepare the request with AI override settings if enabled
    const requestData: any = {
      input: input.trim(),
      options: {
        stream: options.stream
      }
    };

    // Apply AI model overrides if enabled
    if (aiOverride.enabled) {
      requestData.ai_override = {
        provider: aiOverride.provider,
        model: aiOverride.model,
        temperature: aiOverride.temperature,
        max_tokens: aiOverride.maxTokens,
        base_url: aiOverride.baseUrl,
        custom_provider_name: aiOverride.customProviderName,
        api_key_id: aiOverride.apiKeyId // Pass the selected API key ID
      };
    }

    // Use streaming for progress updates
    if (options.stream) {
      await executeAgentWithProgress(requestData, startTime);
    } else {
      const response = await api.agents.invoke(agent.agent_id, requestData);
      await handleAgentResponse(response, startTime);
    }
  };

  const updateRealtimeResponse = (progressData: any) => {
    const timestamp = new Date().toLocaleTimeString();
    const stage = progressData.stage || "overview";

    // Update execution state
    setExecutionState(prev => ({
      ...prev,
      currentStage: stage,
      currentStep: progressData.step_name || prev.currentStep,
    }));

    // Update progress state for legacy support
    setProgress({
      stage: stage,
      message: progressData.message || "",
      progress: progressData.progress || 0,
      currentStep: progressData.current_step || 0,
      totalSteps: progressData.total_steps || 0,
      stepName: progressData.step_name || "",
      assignee: progressData.assignee || ""
    });

    // Extract and track variable placeholders from progress data
    console.log("🔍 [VARIABLE DEBUG] Processing progress data:", {
      stage: progressData.stage,
      message: progressData.message,
      hasContextData: !!progressData.context_data,
      hasVariablesResolved: !!progressData.variables_resolved
    });

    if (progressData.context_data || progressData.variables_resolved) {
      const stepName = progressData.step_name || stage;
      const assignee = progressData.assignee || "system";
      // Calculate correct step index using the helper function
      const stepIndex = calculateStepIndex(assignee, stepName, progressData.current_step || 0);

      // Handle context_data (from backend context service) with inter-agent detection
      if (progressData.context_data) {
        console.log("🔍 [VARIABLE DEBUG] Processing context_data:", progressData.context_data);
        Object.entries(progressData.context_data).forEach(([key, value]) => {
          if (typeof value === 'string' && value.trim()) {
            const placeholderName = `{${key}}`;
            const interAgentInfo = detectInterAgentCommunication(
              placeholderName,
              assignee,
              stepName,
              progressData
            );

            console.log("🔍 [INTER-AGENT DEBUG] Detected communication pattern:", {
              placeholderName,
              ...interAgentInfo
            });

            updateVariablePlaceholder(
              placeholderName,
              value,
              stepName,
              assignee,
              `Context data from ${stepName}`,
              stepIndex,
              interAgentInfo.communicationType,
              interAgentInfo.destinationAgents,
              interAgentInfo.dependsOn,
              interAgentInfo.contextDependencies
            );
          }
        });
      }

      // Handle variables_resolved (explicit variable tracking) with inter-agent detection
      if (progressData.variables_resolved && Array.isArray(progressData.variables_resolved)) {
        progressData.variables_resolved.forEach((variable: any) => {
          const placeholderName = variable.placeholder_name || variable.name;
          const sourceAgent = variable.source_agent || assignee;
          const sourceStep = variable.source_step || stepName;

          const interAgentInfo = detectInterAgentCommunication(
            placeholderName,
            sourceAgent,
            sourceStep,
            { ...progressData, ...variable }
          );

          console.log("🔍 [INTER-AGENT DEBUG] Explicit variable with communication pattern:", {
            placeholderName,
            sourceAgent,
            ...interAgentInfo
          });

          updateVariablePlaceholder(
            placeholderName,
            variable.value || variable.resolved_value,
            sourceStep,
            sourceAgent,
            variable.semantic_description || variable.description || "",
            stepIndex,
            interAgentInfo.communicationType,
            interAgentInfo.destinationAgents,
            interAgentInfo.dependsOn,
            interAgentInfo.contextDependencies
          );
        });
      }

      // Enhanced simulation for intermediate workflow variables
      if (!progressData.variables_resolved) {
        console.log("🔍 [VARIABLE DEBUG] Running enhanced simulation logic:", {
          stage: progressData.stage,
          assignee: progressData.assignee,
          stepIndex: stepIndex,
          message: progressData.message?.substring(0, 50)
        });

        // Always create user input variable at the start (more aggressive triggering)
        if (progressData.stage === "initializing" || progressData.stage === "connecting" ||
            progressData.stage === "overview" || progressData.message?.includes("连接") ||
            progressData.message?.includes("开始")) {
          console.log("🔍 [VARIABLE DEBUG] Creating user.requirements variable");

          // Determine destination agents (first agent in workflow)
          const destinationAgents = workflowSteps.length > 0 ? [workflowSteps[0].assignee] : [];

          updateVariablePlaceholder(
            "{user.requirements}",
            input.trim(),
            "user_input",
            "user",
            "用户输入的需求和任务描述",
            0,
            "user-input",
            destinationAgents,
            [],
            []
          );
        }

        // NEW: Assignee-based variable creation for intermediate agents
        const currentAssignee = progressData.assignee || assignee;
        if (currentAssignee && stepIndex > 0) {
          console.log("🔍 [INTERMEDIATE DEBUG] Processing intermediate agent:", {
            assignee: currentAssignee,
            stepIndex: stepIndex,
            stage: progressData.stage
          });

          // Create variables based on current assignee regardless of stage name
          if (currentAssignee.toLowerCase().includes('planner') ||
              currentAssignee.toLowerCase().includes('plan') ||
              (progressData.message && (progressData.message.includes('规划') || progressData.message.includes('计划')))) {
            console.log("🔍 [INTERMEDIATE DEBUG] Creating planner variable for assignee:", currentAssignee);

            const plannerIndex = workflowSteps.findIndex(step =>
              step.assignee === currentAssignee || step.assignee.toLowerCase().includes('planner')
            );
            const destinationAgents = plannerIndex >= 0 && plannerIndex < workflowSteps.length - 1
              ? workflowSteps.slice(plannerIndex + 1).map(step => step.assignee)
              : ["analyst", "executor"];

            updateVariablePlaceholder(
              `{${currentAssignee}.task_breakdown}`,
              `任务分解和执行计划（由${currentAssignee}生成）：\n1. 需求分析\n2. 方案设计\n3. 实施执行\n4. 结果验证`,
              "planning",
              currentAssignee,
              `${currentAssignee}的任务分解结果`,
              stepIndex,
              "inter-agent",
              destinationAgents,
              ["{user.requirements}"],
              ["user_input"]
            );
          }

          if (currentAssignee.toLowerCase().includes('analyst') ||
              currentAssignee.toLowerCase().includes('analysis') ||
              (progressData.message && progressData.message.includes('分析'))) {
            console.log("🔍 [INTERMEDIATE DEBUG] Creating analyst variable for assignee:", currentAssignee);

            const analystIndex = workflowSteps.findIndex(step =>
              step.assignee === currentAssignee || step.assignee.toLowerCase().includes('analyst')
            );
            const destinationAgents = analystIndex >= 0 && analystIndex < workflowSteps.length - 1
              ? workflowSteps.slice(analystIndex + 1).map(step => step.assignee)
              : ["executor", "reviewer"];

            updateVariablePlaceholder(
              `{${currentAssignee}.analysis_results}`,
              `详细分析结果（由${currentAssignee}生成）：基于用户需求进行的深度分析，包含关键要点和建议方案...`,
              "analysis",
              currentAssignee,
              `${currentAssignee}的分析结果`,
              stepIndex,
              "inter-agent",
              destinationAgents,
              ["{user.requirements}"],
              ["user_input"]
            );
          }

          if (currentAssignee.toLowerCase().includes('executor') ||
              currentAssignee.toLowerCase().includes('execute') ||
              (progressData.message && (progressData.message.includes('执行') || progressData.message.includes('生成')))) {
            console.log("🔍 [INTERMEDIATE DEBUG] Creating executor variable for assignee:", currentAssignee);

            const executorIndex = workflowSteps.findIndex(step =>
              step.assignee === currentAssignee || step.assignee.toLowerCase().includes('executor')
            );
            const destinationAgents = executorIndex >= 0 && executorIndex < workflowSteps.length - 1
              ? workflowSteps.slice(executorIndex + 1).map(step => step.assignee)
              : ["reviewer"];

            updateVariablePlaceholder(
              `{${currentAssignee}.execution_output}`,
              `执行结果（由${currentAssignee}生成）：按照计划完成的具体实施内容和产出物...`,
              "execution",
              currentAssignee,
              `${currentAssignee}的执行结果`,
              stepIndex,
              "inter-agent",
              destinationAgents,
              ["{user.requirements}"],
              ["user_input"]
            );
          }

          if (currentAssignee.toLowerCase().includes('reviewer') ||
              currentAssignee.toLowerCase().includes('review') ||
              (progressData.message && (progressData.message.includes('审查') || progressData.message.includes('检查')))) {
            console.log("🔍 [INTERMEDIATE DEBUG] Creating reviewer variable for assignee:", currentAssignee);

            updateVariablePlaceholder(
              `{${currentAssignee}.review_feedback}`,
              `审查反馈（由${currentAssignee}生成）：对执行结果的质量评估和改进建议...`,
              "review",
              currentAssignee,
              `${currentAssignee}的反馈意见`,
              stepIndex,
              "inter-agent",
              [],
              ["{user.requirements}"],
              ["user_input"]
            );
          }
        }

        // Enhanced stage-based simulation (more flexible triggering)
        if (progressData.stage === "planning" || progressData.message?.includes("规划") ||
            progressData.message?.includes("计划") || progressData.message?.includes("分析") ||
            progressData.stage === "analysis" || stepIndex === 1) {
          console.log("🔍 [VARIABLE DEBUG] Creating planner.task_breakdown variable (stage-based)");

          // Find subsequent agents that might use this variable
          const plannerIndex = workflowSteps.findIndex(step => step.assignee === "planner");
          const destinationAgents = plannerIndex >= 0 && plannerIndex < workflowSteps.length - 1
            ? workflowSteps.slice(plannerIndex + 1).map(step => step.assignee)
            : ["analyst", "executor"];

          updateVariablePlaceholder(
            "{planner.task_breakdown}",
            "任务分解和执行计划：\n1. 需求分析\n2. 方案设计\n3. 实施执行\n4. 结果验证",
            "planning",
            "planner",
            "规划师的任务分解结果",
            stepIndex,
            "inter-agent",
            destinationAgents,
            ["{user.requirements}"],
            ["user_input"]
          );
        }

        if (progressData.stage === "analysis" || progressData.message?.includes("分析") ||
            progressData.message?.includes("处理") || progressData.stage === "processing" ||
            stepIndex === 2) {
          console.log("🔍 [VARIABLE DEBUG] Creating analyst.analysis_results variable (stage-based)");

          // Find subsequent agents that might use this variable
          const analystIndex = workflowSteps.findIndex(step => step.assignee === "analyst");
          const destinationAgents = analystIndex >= 0 && analystIndex < workflowSteps.length - 1
            ? workflowSteps.slice(analystIndex + 1).map(step => step.assignee)
            : ["executor", "reviewer"];

          updateVariablePlaceholder(
            "{analyst.analysis_results}",
            "详细分析结果：基于用户需求进行的深度分析，包含关键要点和建议方案...",
            "analysis",
            "analyst",
            "分析师的分析结果",
            stepIndex,
            "inter-agent",
            destinationAgents,
            ["{user.requirements}", "{planner.task_breakdown}"],
            ["user_input", "planning"]
          );
        }

        if (progressData.stage === "execution" || progressData.message?.includes("执行") ||
            progressData.message?.includes("生成") || progressData.message?.includes("完成") ||
            stepIndex === 3) {
          console.log("🔍 [VARIABLE DEBUG] Creating executor.execution_output variable (stage-based)");

          // Find subsequent agents that might use this variable
          const executorIndex = workflowSteps.findIndex(step => step.assignee === "executor");
          const destinationAgents = executorIndex >= 0 && executorIndex < workflowSteps.length - 1
            ? workflowSteps.slice(executorIndex + 1).map(step => step.assignee)
            : ["reviewer"];

          updateVariablePlaceholder(
            "{executor.execution_output}",
            "执行结果：按照计划完成的具体实施内容和产出物...",
            "execution",
            "executor",
            "执行者的执行结果",
            stepIndex,
            "inter-agent",
            destinationAgents,
            ["{user.requirements}", "{planner.task_breakdown}", "{analyst.analysis_results}"],
            ["user_input", "planning", "analysis"]
          );
        }

        if (progressData.stage === "review" || progressData.message?.includes("审查") ||
            progressData.message?.includes("检查") || progressData.stage === "completed" ||
            stepIndex === 4 || stepIndex >= 4) {
          console.log("🔍 [VARIABLE DEBUG] Creating reviewer.review_feedback variable (stage-based)");

          updateVariablePlaceholder(
            "{reviewer.review_feedback}",
            "审查反馈：对执行结果的质量评估和改进建议...",
            "review",
            "reviewer",
            "审查员的反馈意见",
            stepIndex,
            "inter-agent",
            [], // Final step, no destination agents
            ["{executor.execution_output}", "{analyst.analysis_results}"],
            ["execution", "analysis"]
          );
        }

        // Enhanced fallback - create variables for any intermediate agent
        if (progressData.message && stepIndex > 0) {
          console.log("🔍 [FALLBACK DEBUG] Creating comprehensive fallback variables:", {
            stepIndex: stepIndex,
            assignee: progressData.assignee,
            message: progressData.message?.substring(0, 50)
          });

          // Create step-based variable
          updateVariablePlaceholder(
            `{step_${stepIndex}.output}`,
            `步骤 ${stepIndex} 的输出：${progressData.message}`,
            `step_${stepIndex}`,
            progressData.assignee || "agent",
            `第 ${stepIndex} 步的执行结果`,
            stepIndex,
            "inter-agent",
            stepIndex < 4 ? ["next_agent"] : [],
            stepIndex > 1 ? [`{step_${stepIndex-1}.output}`] : ["{user.requirements}"],
            stepIndex > 1 ? [`step_${stepIndex-1}`] : ["user_input"]
          );

          // Create agent-specific variable if assignee is available
          if (progressData.assignee && progressData.assignee !== "system" && progressData.assignee !== "user") {
            console.log("🔍 [FALLBACK DEBUG] Creating agent-specific fallback variable for:", progressData.assignee);

            // Find subsequent agents for destination mapping
            const currentAgentIndex = workflowSteps.findIndex(step => step.assignee === progressData.assignee);
            const destinationAgents = currentAgentIndex >= 0 && currentAgentIndex < workflowSteps.length - 1
              ? workflowSteps.slice(currentAgentIndex + 1).map(step => step.assignee)
              : [];

            updateVariablePlaceholder(
              `{${progressData.assignee}.intermediate_output}`,
              `${progressData.assignee}的中间输出：${progressData.message}`,
              `${progressData.assignee}_step`,
              progressData.assignee,
              `${progressData.assignee}在第${stepIndex}步的输出结果`,
              stepIndex,
              "inter-agent",
              destinationAgents,
              stepIndex > 1 ? ["{user.requirements}"] : [],
              stepIndex > 1 ? ["user_input"] : []
            );
          }
        }

        // AGGRESSIVE: Create variables for ANY step with an index > 0, regardless of other conditions
        if (stepIndex > 0 && !progressData.variables_resolved) {
          console.log("🔍 [AGGRESSIVE DEBUG] Ensuring intermediate variable creation for step:", stepIndex);

          // Map step index to typical agent roles
          const stepToAgentMap: { [key: number]: string } = {
            1: "planner",
            2: "analyst",
            3: "executor",
            4: "reviewer"
          };

          const expectedAgent = stepToAgentMap[stepIndex];
          if (expectedAgent) {
            console.log("🔍 [AGGRESSIVE DEBUG] Creating variable for expected agent:", expectedAgent);

            const agentIndex = workflowSteps.findIndex(step =>
              step.assignee.toLowerCase().includes(expectedAgent) || step.assignee === expectedAgent
            );
            const destinationAgents = agentIndex >= 0 && agentIndex < workflowSteps.length - 1
              ? workflowSteps.slice(agentIndex + 1).map(step => step.assignee)
              : [];

            updateVariablePlaceholder(
              `{${expectedAgent}.step_${stepIndex}_output}`,
              `${expectedAgent}在步骤${stepIndex}的输出：${progressData.message || '正在处理中...'}`,
              `${expectedAgent}_step_${stepIndex}`,
              expectedAgent,
              `${expectedAgent}在第${stepIndex}步的处理结果`,
              stepIndex,
              "inter-agent",
              destinationAgents,
              stepIndex > 1 ? ["{user.requirements}"] : [],
              stepIndex > 1 ? ["user_input"] : []
            );
          }
        }
      }
    }

    // Determine target tab based on workflow steps or stage
    let targetTab = "overview";

    if (useWorkflowTabs && progressData.step_name) {
      // If using workflow tabs and we have a step name, use the step name as tab
      const stepExists = workflowSteps.find(step => step.name === progressData.step_name);
      if (stepExists) {
        targetTab = progressData.step_name;

        // Enable the workflow step tab and switch to it
        enableExecutionTab(targetTab, true);

        // Ensure the workflow step tab exists (legacy support)
        setAvailableTabs(prev => {
          if (!prev.includes(targetTab)) {
            return [...prev, targetTab];
          }
          return prev;
        });

        // Auto-switch to workflow step tab (legacy support)
        setActiveResponseTab(targetTab);
      }
    } else if (!useWorkflowTabs) {
      // Use stage-based tabs (legacy behavior)
      targetTab = stage;

      // Ensure the stage tab exists
      setAvailableTabs(prev => {
        if (!prev.includes(stage)) {
          return [...prev, stage];
        }
        return prev;
      });

      // Auto-switch to new stage tab
      setActiveResponseTab(stage);
    }

    // Update content based on stage and target tab
    let content = "";
    switch (stage) {
      case "initializing":
        content = `🔄 [${timestamp}] ${progressData.message}\n\n`;
        break;

      case "planning":
        content = `📋 [${timestamp}] ${progressData.message}\n\n`;
        break;

      case "executing":
        if (progressData.step_name && progressData.assignee) {
          content = useWorkflowTabs
            ? `⚡ [${timestamp}] 开始执行工作流步骤\n` +
              `   步骤: ${progressData.step_name}\n` +
              `   负责人: ${progressData.assignee}\n` +
              `   进度: ${progressData.progress}% (${progressData.current_step}/${progressData.total_steps})\n` +
              `📝 实时输出:\n`
            : `⚡ [${timestamp}] 开始执行: ${progressData.step_name}\n` +
              `   负责人: ${progressData.assignee}\n` +
              `   进度: ${progressData.progress}% (${progressData.current_step}/${progressData.total_steps})\n` +
              `📝 实时输出:\n`;
        }
        break;

      case "streaming":
        // Handle real-time streaming content - always append approach
        if (progressData.streaming_content) {
          content = progressData.streaming_content;
        }
        break;

      case "step_completed":
        // Add completion message with clear separation
        content = useWorkflowTabs
          ? `\n✅ [${timestamp}] 工作流步骤完成: ${progressData.step_name || progressData.message}\n` +
            `${'─'.repeat(50)}\n\n`
          : `\n✅ [${timestamp}] 步骤完成: ${progressData.message}\n` +
            `${'─'.repeat(50)}\n\n`;

        // Mark the current step as completed
        if (progressData.step_name) {
          completeExecutionTab(progressData.step_name);
        }
        break;

      case "completed":
        // Only show completion message, not the final output (already shown in streaming)
        content = `🎉 [${timestamp}] ${progressData.message}\n\n`;

        // Enable results tab and mark execution as completed
        enableExecutionTab("results", false);
        setExecutionState(prev => ({
          ...prev,
          isRunning: false,
          hasResults: true
        }));

        // Show a subtle notification that results are ready
        setTimeout(() => {
          console.log("✅ 测试完成！结果已准备就绪，可在结果标签页查看。");
        }, 1000);
        break;

      case "connecting":
        content = `🔗 [${timestamp}] ${progressData.message}\n\n`;
        targetTab = "overview";
        break;

      default:
        if (progressData.message) {
          content = `ℹ️ [${timestamp}] ${progressData.message}\n\n`;
          targetTab = "overview";
        }
        break;
    }

    // Update both new execution tabs and legacy stage responses
    if (content) {
      // Update execution tab content
      const currentTab = executionTabs.find(tab => tab.id === targetTab);
      if (currentTab) {
        updateExecutionTab(targetTab, currentTab.content + content);
      }

      // Update legacy stage responses for compatibility
      setStageResponses(prev => ({
        ...prev,
        [targetTab]: (prev[targetTab] || "") + content
      }));
    }

    // Also update the legacy realtimeResponse for backward compatibility
    setRealtimeResponse(prev => {
      switch (stage) {
        case "initializing":
          return prev + `🔄 [${timestamp}] ${progressData.message}\n\n`;
        case "planning":
          return prev + `📋 [${timestamp}] ${progressData.message}\n\n`;
        case "executing":
          if (progressData.step_name && progressData.assignee) {
            return prev + `⚡ [${timestamp}] 开始执行: ${progressData.step_name}\n` +
              `   负责人: ${progressData.assignee}\n` +
              `   进度: ${progressData.progress}% (${progressData.current_step}/${progressData.total_steps})\n` +
              `📝 实时输出:\n`;
          }
          return prev;
        case "streaming":
          if (progressData.streaming_content) {
            return prev + progressData.streaming_content;
          }
          return prev;
        case "step_completed":
          return prev + `\n✅ [${timestamp}] 步骤完成: ${progressData.message}\n` +
            `${'─'.repeat(50)}\n\n`;
        case "completed":
          return prev + `🎉 [${timestamp}] ${progressData.message}\n\n`;
        default:
          if (progressData.message) {
            return prev + `ℹ️ [${timestamp}] ${progressData.message}\n\n`;
          }
          return prev;
      }
    });
  };

  const executeAgentWithProgress = async (requestData: any, startTime: number): Promise<void> => {
    setShowProgress(true);
    setRealtimeResponse(""); // Clear previous response
    setResponse(null); // Clear previous response
    setProgressUpdates([]); // Clear previous progress updates

    // Clear multi-tab responses and reset tabs based on workflow
    setStageResponses({
      overview: "🔗 正在连接到代理...\n\n"
    });
    setActiveResponseTab("overview");

    // Initialize tabs based on workflow steps
    if (useWorkflowTabs && workflowSteps.length > 0) {
      const workflowTabNames = workflowSteps.map(step => step.name);
      setAvailableTabs(["overview", ...workflowTabNames]);
    } else {
      setAvailableTabs(["overview"]);
    }

    setProgress({
      stage: "connecting",
      message: "正在连接到代理...",
      progress: 0,
      currentStep: 0,
      totalSteps: 0,
      stepName: "",
      assignee: ""
    });

    // Initialize response with connection message
    setRealtimeResponse("🔗 正在连接到代理...\n\n");

    // Start test execution and get test_id from backend
    let testId: string;
    try {
      console.log("Starting test execution...");

      // Get API key name for history record
      const apiKeyName = aiOverride.apiKeyId && aiOverride.apiKeyId !== "default"
        ? apiKeys.find(key => String(key.id) === String(aiOverride.apiKeyId))?.name || '自定义密钥'
        : '默认密钥';

      // Create comprehensive configuration record for history
      const comprehensiveConfig = {
        // AI Model Configuration
        ...(requestData.ai_override || {}),

        // Execution Configuration
        stream_mode: options.stream,
        execution_method: options.stream ? "streaming" : "sync",

        // Additional metadata
        advanced_config_enabled: aiOverride.enabled,
        timestamp: new Date().toISOString()
      };

      const startResponse = await api.testExecution.start({
        agent_id: agent.agent_id,
        input_text: requestData.input || "",
        ai_config_override: comprehensiveConfig,
        api_key_id: aiOverride.apiKeyId && aiOverride.apiKeyId !== "default" ? Number(aiOverride.apiKeyId) : null,
        api_key_name: apiKeyName,
        input_metadata: {
          timestamp: new Date().toISOString(),
          user_agent: navigator.userAgent,
          execution_method: options.stream ? "streaming" : "sync",
          stream_mode: options.stream
        }
      });

      if (startResponse.success && startResponse.data) {
        testId = startResponse.data.test_id;
        console.log("Test execution started successfully with ID:", testId);
      } else {
        throw new Error("Failed to start test execution");
      }
    } catch (error) {
      console.error("Failed to start test execution:", error);
      setIsLoading(false);
      alert("启动测试执行失败，请重试");
      return;
    }

    // Add test_id to request data for variable tracking
    requestData.test_id = testId;

    try {
      await api.agents.invokeStreamWithProgress(agent.agent_id, requestData, {
        onProgress: (progressData) => {
          console.log("🔍 [PROGRESS DEBUG] Received progress data:", progressData);

          setProgress({
            stage: progressData.stage || "",
            message: progressData.message || "",
            progress: progressData.progress || 0,
            currentStep: progressData.current_step || 0,
            totalSteps: progressData.total_steps || 0,
            stepName: progressData.step_name || "",
            assignee: progressData.assignee || ""
          });

          // Update real-time response based on stage
          updateRealtimeResponse(progressData);

          // Track progress updates for test history
          setProgressUpdates(prev => {
            const newUpdates = [...prev, {
              ...progressData,
              timestamp: new Date().toISOString()
            }];
            console.log("🔍 [PROGRESS DEBUG] Updated progress updates, total count:", newUpdates.length);
            return newUpdates;
          });
        },
        onComplete: async (result) => {
          const endTime = Date.now();
          const responseTime = endTime - startTime;
          const timestamp = new Date().toLocaleTimeString();

          setShowProgress(false);
          setProgress({
            stage: "completed",
            message: "执行完成",
            progress: 100,
            currentStep: 0,
            totalSteps: 0,
            stepName: "",
            assignee: ""
          });

          // Extract final output, execution stages, and AI config
          let finalOutput = "";
          let executionStagesData: any[] = [];
          let aiConfigUsed = null;

          console.log("🔍 [EXECUTION STAGES DEBUG] Processing completion result:", result);
          console.log("🔍 [EXECUTION STAGES DEBUG] Result structure:", {
            hasContent: !!result.content,
            hasFullResult: !!result.full_result,
            hasResults: !!result.results,
            hasExecutionStages: !!result.execution_stages,
            progressUpdatesCount: progressUpdates.length
          });

          if (result.content) {
            finalOutput = typeof result.content === 'string' ? result.content : JSON.stringify(result.content, null, 2);
          } else if (result.full_result) {
            // Extract AI config used from full result
            aiConfigUsed = result.full_result.ai_config_used;

            if (result.full_result.final_output) {
              finalOutput = typeof result.full_result.final_output === 'string' ?
                result.full_result.final_output : JSON.stringify(result.full_result.final_output, null, 2);
            } else if (result.full_result.results && Array.isArray(result.full_result.results)) {
              executionStagesData = result.full_result.results;
              console.log("🔍 [EXECUTION STAGES DEBUG] Found stages in result.full_result.results:", executionStagesData);
              const resultTexts = result.full_result.results.map((res: any, index: number) => {
                const outputText = typeof res.output === 'string' ? res.output : JSON.stringify(res.output, null, 2);
                const assignee = res.assignee || '未知成员';
                return `   步骤 ${index + 1} (${assignee}): ${outputText}`;
              });
              finalOutput = resultTexts.join('\n');
            }
          }

          // Try to extract execution stages from different possible locations
          if (executionStagesData.length === 0) {
            console.log("🔍 [EXECUTION STAGES DEBUG] No stages found in primary location, trying fallbacks...");

            // Check if stages are in result.results
            if (result.results && Array.isArray(result.results)) {
              executionStagesData = result.results;
              console.log("🔍 [EXECUTION STAGES DEBUG] Found execution stages in result.results:", executionStagesData);
            }
            // Check if stages are in result.execution_stages
            else if (result.execution_stages && Array.isArray(result.execution_stages)) {
              executionStagesData = result.execution_stages;
              console.log("🔍 [EXECUTION STAGES DEBUG] Found execution stages in result.execution_stages:", executionStagesData);
            }
            // Try to construct stages from progress updates if available
            else if (progressUpdates.length > 0) {
              console.log("🔍 [EXECUTION STAGES DEBUG] Trying to construct from progress updates. Total updates:", progressUpdates.length);
              console.log("🔍 [EXECUTION STAGES DEBUG] Progress updates:", progressUpdates);

              const stageUpdates = progressUpdates.filter(update =>
                update.stepName && update.assignee && (update.stage === 'step_completed' || update.stage === 'executing')
              );
              console.log("🔍 [EXECUTION STAGES DEBUG] Filtered stage updates:", stageUpdates);

              if (stageUpdates.length > 0) {
                executionStagesData = stageUpdates.map((update, index) => ({
                  step_name: update.stepName,
                  assignee: update.assignee,
                  output: update.message || `步骤完成: ${update.stepName}`,
                  index: index + 1
                }));
                console.log("🔍 [EXECUTION STAGES DEBUG] Constructed execution stages from progress updates:", executionStagesData);
              } else {
                console.log("🔍 [EXECUTION STAGES DEBUG] No suitable progress updates found for stage construction");
              }
            } else {
              console.log("🔍 [EXECUTION STAGES DEBUG] No progress updates available");
            }
          } else {
            console.log("🔍 [EXECUTION STAGES DEBUG] Using stages from primary location, count:", executionStagesData.length);
          }

          // Store execution stages for step-by-step results display
          console.log("🔍 [EXECUTION STAGES DEBUG] Final execution stages to set:", executionStagesData);
          console.log("🔍 [EXECUTION STAGES DEBUG] Execution stages count:", executionStagesData.length);
          if (executionStagesData.length > 0) {
            console.log("🔍 [EXECUTION STAGES DEBUG] Sample stage structure:", executionStagesData[0]);
          }
          setExecutionStages(executionStagesData);

          // Add final completion message to real-time response (without repeating content)
          setRealtimeResponse(prev => prev + `\n🏁 [${timestamp}] 执行完成！执行时间: ${responseTime}ms\n`);

          // Update test history with completion data
          try {
            console.log("Updating test history with completion data:", testId);

            // Convert variable placeholders to backend format with generated content
            const contextPlaceholdersUsed = variablePlaceholders.map(placeholder => {
              // Find corresponding execution stage output for this variable
              let generatedContent = placeholder.value || '';

              // Try to find the execution stage that corresponds to this variable's source agent
              if (executionStagesData && executionStagesData.length > 0) {
                const correspondingStage = executionStagesData.find(stage =>
                  stage.assignee === placeholder.sourceAgent ||
                  stage.step_name?.includes(placeholder.sourceAgent) ||
                  stage.assignee?.toLowerCase().includes(placeholder.sourceAgent.toLowerCase())
                );

                if (correspondingStage && correspondingStage.output) {
                  generatedContent = typeof correspondingStage.output === 'string'
                    ? correspondingStage.output
                    : JSON.stringify(correspondingStage.output, null, 2);
                }
              }

              // If still no content, try to extract from final output
              if (!generatedContent && finalOutput) {
                // Look for patterns that might contain this variable's content
                const variableName = placeholder.placeholderName.replace(/[{}]/g, '');
                const outputLines = finalOutput.split('\n');
                const relevantLine = outputLines.find(line =>
                  line.includes(placeholder.sourceAgent) ||
                  line.includes(variableName)
                );
                if (relevantLine) {
                  generatedContent = relevantLine.trim();
                }
              }

              return {
                name: placeholder.placeholderName.replace(/[{}]/g, ''), // Remove braces
                placeholder: placeholder.placeholderName,
                variable_type: placeholder.communicationType,
                source_agent: placeholder.sourceAgent,
                destination_agents: placeholder.destinationAgents,
                semantic_description: placeholder.semanticDescription,
                workflow_step: placeholder.stepIndex,
                is_required: true,
                resolved_value: placeholder.value,
                generated_content: generatedContent, // 新增：记录生成的内容
                resolution_status: placeholder.value ? 'resolved' : 'pending',
                resolved_at: placeholder.resolvedAt
              };
            });

            // Convert team interactions from variable data flow chains
            const teamMemberInteractions = variablePlaceholders
              .filter(placeholder => placeholder.dataFlowChain && placeholder.dataFlowChain.length > 1)
              .flatMap(placeholder =>
                placeholder.dataFlowChain.slice(1).map((flow, index) => ({
                  source_agent: placeholder.dataFlowChain[index].agent,
                  destination_agent: flow.agent,
                  variable_name: placeholder.placeholderName,
                  variable_value: placeholder.value || '',
                  step_index: placeholder.stepIndex,
                  timestamp: flow.timestamp,
                  interaction_type: placeholder.communicationType
                }))
              );

            const updateResponse = await api.testHistory.update(testId, {
              status: 'completed',
              completed_at: new Date().toISOString(),
              execution_duration_ms: responseTime,
              execution_stages: executionStagesData,
              progress_updates: progressUpdates,
              final_output: finalOutput,
              individual_agent_outputs: individualAgentOutputs,
              agent_execution_sequence: agentExecutionSequence,
              response_metadata: {
                responseTime,
                status: 'completed',
                executionMethod: 'streaming',
                timestamp: new Date().toISOString()
              },
              context_placeholders_used: contextPlaceholdersUsed,
              team_member_interactions: teamMemberInteractions,
              context_summary: {
                total_variables: variablePlaceholders.length,
                resolved_variables: variablePlaceholders.filter(p => p.value).length,
                inter_agent_communications: teamMemberInteractions.length,
                variable_types: {
                  'inter-agent': variablePlaceholders.filter(p => p.communicationType === 'inter-agent').length,
                  'user-input': variablePlaceholders.filter(p => p.communicationType === 'user-input').length,
                  'system': variablePlaceholders.filter(p => p.communicationType === 'system').length,
                  'internal': variablePlaceholders.filter(p => p.communicationType === 'internal').length
                }
              }
            });
            console.log("Test history updated successfully:", updateResponse);
          } catch (error) {
            console.error("Failed to update test history:", error);
          }

          // Set the final response for results tab display
          // Use the realtime response content as the final response
          setResponse(realtimeResponse || finalOutput || "执行完成，但没有返回具体结果");

          // Get API key name for metadata
          const apiKeyUsed = aiOverride.apiKeyId && aiOverride.apiKeyId !== "default"
            ? apiKeys.find(key => String(key.id) === String(aiOverride.apiKeyId))?.name || '自定义密钥'
            : '默认密钥';

          // Set comprehensive response metadata
          setResponseMetadata({
            // Performance metrics
            responseTime,
            tokensUsed: 0, // Streaming doesn't provide token usage yet
            inputTokens: 0,
            outputTokens: 0,

            // AI Model Configuration
            modelUsed: aiConfigUsed?.model || (aiOverride.enabled && aiOverride.model ? aiOverride.model : '默认模型'),
            providerUsed: aiConfigUsed?.provider || (aiOverride.enabled ? (aiOverride.customProviderName || aiOverride.provider) : '默认提供商'),
            temperatureUsed: aiConfigUsed?.temperature !== undefined ? aiConfigUsed.temperature : (aiOverride.enabled ? aiOverride.temperature : 0.7),
            maxTokensUsed: aiConfigUsed?.max_tokens || (aiOverride.enabled ? aiOverride.maxTokens : 2000),

            // Connection Configuration
            baseUrlUsed: aiConfigUsed?.base_url || (aiOverride.enabled && aiOverride.baseUrl ? aiOverride.baseUrl : '默认端点'),
            customProviderNameUsed: aiConfigUsed?.custom_provider_name || (aiOverride.enabled && aiOverride.customProviderName ? aiOverride.customProviderName : null),
            apiKeyUsed,

            // Execution Configuration
            executionMethod: 'streaming',
            streamModeUsed: options.stream,

            // Status
            status: 'completed'
          });
          setIsLoading(false);
          console.log("Test execution completed, response set:", realtimeResponse || finalOutput);

          // Refresh recent tests after completion
          loadRecentTests();
        },
        onError: async (error) => {
          const endTime = Date.now();
          const responseTime = endTime - startTime;
          const timestamp = new Date().toLocaleTimeString();

          setShowProgress(false);
          // Append error to existing content instead of overwriting
          setRealtimeResponse(prev => prev + `\n❌ [${timestamp}] 执行失败: ${error}\n`);
          setResponseMetadata({
            error: true,
            message: error,
            responseTime,
            executionMethod: 'streaming'
          });

          // Update test history with error information
          try {
            // Convert variable placeholders to backend format (even for failed tests)
            const contextPlaceholdersUsed = variablePlaceholders.map(placeholder => ({
              name: placeholder.placeholderName.replace(/[{}]/g, ''), // Remove braces
              placeholder: placeholder.placeholderName,
              variable_type: placeholder.communicationType,
              source_agent: placeholder.sourceAgent,
              destination_agents: placeholder.destinationAgents,
              semantic_description: placeholder.semanticDescription,
              workflow_step: placeholder.stepIndex,
              is_required: true,
              resolved_value: placeholder.value,
              generated_content: placeholder.value || '', // 记录已有的值作为生成内容
              resolution_status: placeholder.value ? 'resolved' : 'failed',
              resolved_at: placeholder.resolvedAt
            }));

            await api.testHistory.update(testId, {
              status: 'failed',
              completed_at: new Date().toISOString(),
              execution_duration_ms: responseTime,
              error_message: typeof error === 'string' ? error : JSON.stringify(error),
              error_details: {
                timestamp: new Date().toISOString(),
                executionMethod: 'streaming',
                error: error
              },
              context_placeholders_used: contextPlaceholdersUsed,
              context_summary: {
                total_variables: variablePlaceholders.length,
                resolved_variables: variablePlaceholders.filter(p => p.value).length,
                execution_failed: true
              }
            });
          } catch (updateError) {
            console.warn("Failed to update test history with error:", updateError);
          }
        }
      });
    } catch (error) {
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      const timestamp = new Date().toLocaleTimeString();
      setShowProgress(false);
      // Append connection error to existing content instead of overwriting
      setRealtimeResponse(prev => prev + `\n🔌 [${timestamp}] 连接错误: ${error instanceof Error ? error.message : "未知错误"}\n`);
      setResponseMetadata({
        error: true,
        message: error instanceof Error ? error.message : "Unknown error",
        responseTime,
        executionMethod: 'streaming'
      });

      // Update test history with connection error
      try {
        // Convert variable placeholders to backend format (even for connection errors)
        const contextPlaceholdersUsed = variablePlaceholders.map(placeholder => ({
          name: placeholder.placeholderName.replace(/[{}]/g, ''), // Remove braces
          placeholder: placeholder.placeholderName,
          variable_type: placeholder.communicationType,
          source_agent: placeholder.sourceAgent,
          destination_agents: placeholder.destinationAgents,
          semantic_description: placeholder.semanticDescription,
          workflow_step: placeholder.stepIndex,
          is_required: true,
          resolved_value: placeholder.value,
          generated_content: placeholder.value || '', // 记录已有的值作为生成内容
          resolution_status: 'failed',
          resolved_at: placeholder.resolvedAt
        }));

        await api.testHistory.update(testId, {
          status: 'failed',
          completed_at: new Date().toISOString(),
          execution_duration_ms: responseTime,
          error_message: error instanceof Error ? error.message : "Connection error",
          error_details: {
            timestamp: new Date().toISOString(),
            executionMethod: 'streaming',
            errorType: 'connection_error',
            error: error instanceof Error ? error.message : "Unknown connection error"
          },
          context_placeholders_used: contextPlaceholdersUsed,
          context_summary: {
            total_variables: variablePlaceholders.length,
            resolved_variables: variablePlaceholders.filter(p => p.value).length,
            connection_failed: true
          }
        });
      } catch (updateError) {
        console.warn("Failed to update test history with connection error:", updateError);
      }
    }
  };

  const handleAgentResponse = async (response: any, startTime: number): Promise<void> => {
    const endTime = Date.now();
    const responseTime = endTime - startTime;

    if (response.success && response.data) {
      // Handle successful response
      let finalOutput = "";
      let actualAiConfig = null;

      if (response.data.status === "success" || response.data.status === "completed") {
        // Handle config-driven agent response
        if (response.data.final_output) {
          finalOutput = typeof response.data.final_output === 'string' ? response.data.final_output : JSON.stringify(response.data.final_output, null, 2);
        } else if (response.data.output) {
          finalOutput = typeof response.data.output === 'string' ? response.data.output : JSON.stringify(response.data.output, null, 2);
        } else if (response.data.results && Array.isArray(response.data.results)) {
          // Extract individual agent outputs for tabbed display
          const agentOutputs = response.data.results.map((result: any, index: number) => ({
            step_index: index,
            step_name: result.step || result.name || `步骤 ${index + 1}`,
            agent_name: result.assignee || '未知成员',
            agent_role: result.member_role || 'specialist',
            output: typeof result.output === 'string' ? result.output : JSON.stringify(result.output, null, 2),
            status: result.status || 'completed',
            execution_method: result.execution_method || 'unknown',
            ai_config_used: result.ai_config_used || {},
            capabilities_used: result.capabilities_used || [],
            timestamp: result.timestamp || new Date().toISOString(),
            description: result.description || '',
            error: result.error,
            total_steps: response.data.results.length
          }));
          setIndividualAgentOutputs(agentOutputs);

          // Create execution sequence
          const executionSequence = response.data.results.map((result: any, index: number) => ({
            sequence_order: index + 1,
            step_name: result.step || result.name || `步骤 ${index + 1}`,
            agent_name: result.assignee || '未知成员',
            agent_role: result.member_role || 'specialist',
            start_time: result.timestamp || new Date().toISOString(),
            status: result.status || 'completed',
            execution_method: result.execution_method || 'unknown',
            has_error: Boolean(result.error)
          }));
          setAgentExecutionSequence(executionSequence);

          // Format workflow results for legacy display
          const resultTexts = response.data.results.map((result: any, index: number) => {
            if (result.output) {
              // Ensure output is converted to string
              const outputText = typeof result.output === 'string' ? result.output : JSON.stringify(result.output, null, 2);
              const assignee = result.assignee || '未知成员';
              return `步骤 ${index + 1} (${assignee}): ${outputText}`;
            }
            return `步骤 ${index + 1}: ${JSON.stringify(result, null, 2)}`;
          });
          finalOutput = resultTexts.join('\n\n');
        } else {
          finalOutput = "执行完成，但没有返回具体结果";
        }

        // Extract AI config used
        actualAiConfig = response.data.ai_config_used;
      } else if (response.data.response) {
        // Direct response format (legacy)
        finalOutput = response.data.response;
      } else {
        finalOutput = `执行状态: ${response.data.status || 'unknown'}`;
      }

      setResponse(finalOutput);

      // Get API key name for metadata
      const apiKeyUsed = aiOverride.apiKeyId && aiOverride.apiKeyId !== "default"
        ? apiKeys.find(key => String(key.id) === String(aiOverride.apiKeyId))?.name || '自定义密钥'
        : '默认密钥';

      // Set comprehensive response metadata
      setResponseMetadata({
        // Performance metrics
        responseTime,
        tokensUsed: response.data.usage?.total_tokens || 0,
        inputTokens: response.data.usage?.input_tokens || 0,
        outputTokens: response.data.usage?.output_tokens || 0,

        // AI Model Configuration
        modelUsed: actualAiConfig?.model || (aiOverride.enabled && aiOverride.model ? aiOverride.model : '默认模型'),
        providerUsed: actualAiConfig?.provider || (aiOverride.enabled ? (aiOverride.customProviderName || aiOverride.provider) : '默认提供商'),
        temperatureUsed: actualAiConfig?.temperature !== undefined ? actualAiConfig.temperature : (aiOverride.enabled ? aiOverride.temperature : 0.7),
        maxTokensUsed: actualAiConfig?.max_tokens || (aiOverride.enabled ? aiOverride.maxTokens : 2000),

        // Connection Configuration
        baseUrlUsed: actualAiConfig?.base_url || (aiOverride.enabled && aiOverride.baseUrl ? aiOverride.baseUrl : '默认端点'),
        customProviderNameUsed: actualAiConfig?.custom_provider_name || (aiOverride.enabled && aiOverride.customProviderName ? aiOverride.customProviderName : null),
        apiKeyUsed,

        // Execution Configuration
        executionMethod: response.data.execution_method || 'sync',
        streamModeUsed: options.stream,

        // Status
        status: response.data.status || 'completed'
      });
    } else {
      throw new Error(response.error?.message || "Agent execution failed");
    }
  };



  return (
    <div className="space-y-6">
      {/* Test Case Management and Batch Testing */}
      <motion.div
        className="flex gap-2"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <TestCaseManager
          agents={agents as any}
          currentInput={input}
          currentAiOverride={aiOverride}
          selectedAgent={agent as any}
          onLoadTestCase={handleLoadTestCase}
          onRunTestCase={handleRunTestCase}
        />
        <BatchTesting
          agent={agent as any}
          agents={agents as any}
        />
      </motion.div>

      {/* Main Tabbed Interface */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Tabs value={mainActiveTab} onValueChange={setMainActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 mobile-tabs">
            <TabsTrigger value="test" className="flex items-center justify-center space-x-1 sm:space-x-2 px-1 sm:px-2">
              <TestTube className="h-4 w-4 flex-shrink-0" />
              <span className="truncate text-xs sm:text-sm">测试</span>
            </TabsTrigger>
            <TabsTrigger
              value="execution"
              disabled={!executionState.isRunning && !executionState.hasResults}
              className="flex items-center justify-center space-x-1 sm:space-x-2 px-1 sm:px-2"
            >
              <Zap className={`h-4 w-4 flex-shrink-0 ${executionState.isRunning ? 'animate-pulse text-blue-500' : ''}`} />
              <span className="truncate text-xs sm:text-sm">执行</span>
              {executionState.isRunning && (
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse ml-1 flex-shrink-0"></div>
              )}
            </TabsTrigger>
            <TabsTrigger
              value="results"
              disabled={!executionState.hasResults}
              className="flex items-center justify-center space-x-1 sm:space-x-2 px-1 sm:px-2"
            >
              <Settings className={`h-4 w-4 flex-shrink-0 ${executionState.hasResults ? 'text-green-500' : ''}`} />
              <span className="truncate text-xs sm:text-sm">结果</span>
              {executionState.hasResults && (
                <div className="w-2 h-2 bg-green-500 rounded-full ml-1 flex-shrink-0"></div>
              )}
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center justify-center space-x-1 sm:space-x-2 px-1 sm:px-2">
              <History className="h-4 w-4 flex-shrink-0" />
              <span className="truncate text-xs sm:text-sm">历史</span>
            </TabsTrigger>
          </TabsList>

          {/* Test Tab Content */}
          <TabsContent value="test" className="w-full space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="w-full space-y-6"
            >
              {/* Selected Agent Info with Change Button */}
              <Card className="border-primary/20">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                        <Bot className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold">{agent.team_name}</h3>
                        <p className="text-sm text-muted-foreground">{agent.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="flex items-center space-x-1">
                        <Users className="h-3 w-3" />
                        <span>{agent.agent_id}</span>
                      </Badge>
                      {onAgentChange && (
                        <Button variant="outline" size="sm" onClick={onAgentChange}>
                          更换Agent
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Test Input Form */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Play className="h-5 w-5 text-primary" />
                    <span>测试输入</span>
                  </CardTitle>
                  <CardDescription>
                    输入您希望AI团队处理的任务或问题
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }} className="space-y-6">
                    {/* Input Area */}
                    <div className="space-y-2">
                      <Label htmlFor="test-input" className="text-base font-medium">
                        任务描述 *
                      </Label>
                      <Textarea
                        id="test-input"
                        placeholder="例如：帮我写一篇关于人工智能发展趋势的技术博客文章..."
                        value={input}
                        onChange={(e) => setInput(e.target.value)}
                        className="min-h-[100px] resize-none"
                        maxLength={2000}
                      />
                      <div className="flex justify-between items-center text-sm text-muted-foreground">
                        <span>
                          {input.length < 5 ? "至少需要5个字符" : "描述越详细，结果越精准"}
                        </span>
                        <span>{input.length}/2000</span>
                      </div>
                    </div>

                    {/* Advanced Configuration - Collapsible */}
                    <Collapsible>
                      <CollapsibleTrigger asChild>
                        <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                          <span className="flex items-center space-x-2">
                            <Settings className="h-4 w-4" />
                            <span>高级配置</span>
                            <Badge variant="outline" className="text-xs">可选</Badge>
                          </span>
                          <ChevronDown className="h-4 w-4 transition-transform" />
                        </Button>
                      </CollapsibleTrigger>
                      <CollapsibleContent className="space-y-4 pt-4">
                        <div className="p-4 border rounded-lg bg-muted/30 space-y-4">
                          <div className="space-y-1 mb-4">
                            <h4 className="text-sm font-medium">自定义AI配置</h4>
                            <p className="text-xs text-muted-foreground">
                              配置自定义AI模型和参数，留空则使用默认设置
                            </p>
                          </div>

                          {/* Custom Model Name */}
                          <div className="space-y-2">
                            <Label htmlFor="custom-model" className="text-sm font-medium">
                              自定义模型名称
                            </Label>
                            <Input
                              id="custom-model"
                              placeholder="例如: gpt-4, claude-3-sonnet, gemini-pro (留空使用默认)"
                              value={aiOverride.model}
                              onChange={(e) => {
                                const value = e.target.value;
                                setAiOverride(prev => ({
                                  ...prev,
                                  model: value,
                                  enabled: Boolean(
                                    value.trim().length > 0 ||
                                    prev.baseUrl ||
                                    prev.apiKeyId ||
                                    (prev.maxTokens !== 2000) ||
                                    (prev.temperature !== 0.7)
                                  )
                                }));
                              }}
                              className="text-sm"
                            />
                            <p className="text-xs text-muted-foreground">
                              输入具体的模型名称，如 gpt-4、claude-3-sonnet 等
                            </p>
                          </div>

                          {/* Temperature */}
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label className="text-sm font-medium">温度 (Temperature)</Label>
                              <span className="text-sm text-muted-foreground">{aiOverride.temperature}</span>
                            </div>
                            <Slider
                              value={[aiOverride.temperature]}
                              onValueChange={([value]) =>
                                setAiOverride(prev => ({
                                  ...prev,
                                  temperature: value,
                                  enabled: Boolean(
                                    prev.model.trim().length > 0 ||
                                    prev.baseUrl ||
                                    prev.apiKeyId ||
                                    (prev.maxTokens !== 2000) ||
                                    (value !== 0.7)
                                  )
                                }))
                              }
                              max={2}
                              min={0}
                              step={0.1}
                              className="w-full"
                            />
                            <p className="text-xs text-muted-foreground">
                              控制输出的随机性，0为确定性，2为最大创造性
                            </p>
                          </div>

                          {/* Max Tokens */}
                          <div className="space-y-2">
                            <Label htmlFor="max-tokens" className="text-sm font-medium">
                              最大令牌数 (Max Tokens)
                            </Label>
                            <Input
                              id="max-tokens"
                              type="number"
                              placeholder="2000 (默认)"
                              value={aiOverride.maxTokens === 2000 ? "" : aiOverride.maxTokens}
                              onChange={(e) => {
                                const inputValue = e.target.value;
                                const numericValue = inputValue === "" ? 2000 : parseInt(inputValue);

                                // Only update if it's a valid number or empty
                                if (inputValue === "" || (!isNaN(numericValue) && numericValue >= 1 && numericValue <= 32000)) {
                                  setAiOverride(prev => ({
                                    ...prev,
                                    maxTokens: numericValue,
                                    enabled: Boolean(prev.model.trim().length > 0 || prev.baseUrl || prev.apiKeyId || (inputValue !== "" && numericValue !== 2000))
                                  }));
                                }
                              }}
                              min={1}
                              max={32000}
                              className="text-sm"
                            />
                            <p className="text-xs text-muted-foreground">
                              限制AI响应的最大长度，留空使用默认值 2000
                            </p>
                          </div>

                          {/* Custom Base URL */}
                          <div className="space-y-2">
                            <Label htmlFor="base-url" className="text-sm font-medium">
                              自定义API端点 (Base URL)
                            </Label>
                            <Input
                              id="base-url"
                              placeholder="例如: https://api.openai.com/v1 或自托管端点 (留空使用默认)"
                              value={aiOverride.baseUrl || ""}
                              onChange={(e) => {
                                const value = e.target.value;
                                setAiOverride(prev => ({
                                  ...prev,
                                  baseUrl: value || undefined,
                                  enabled: Boolean(
                                    prev.model.trim().length > 0 ||
                                    value.trim().length > 0 ||
                                    prev.apiKeyId ||
                                    (prev.maxTokens !== 2000) ||
                                    (prev.temperature !== 0.7)
                                  )
                                }));
                              }}
                              className="text-sm"
                            />
                            <p className="text-xs text-muted-foreground">
                              用于自托管AI服务或代理端点
                            </p>
                          </div>

                          {/* API Key Selection */}
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">AI密钥选择</Label>
                            <div className="relative">
                              <Select
                                value={aiOverride.apiKeyId ? String(aiOverride.apiKeyId) : "default"}
                                disabled={loadingApiKeys}
                                onValueChange={(value) => {
                                  const newApiKeyId = value === "default" ? undefined : value;
                                  setAiOverride(prev => ({
                                    ...prev,
                                    apiKeyId: newApiKeyId,
                                    enabled: Boolean(
                                      prev.model.trim().length > 0 ||
                                      prev.baseUrl ||
                                      (prev.maxTokens !== 2000) ||
                                      (prev.temperature !== 0.7) ||
                                      (value !== "default")
                                    )
                                  }));
                                }}
                              >
                                <SelectTrigger className="text-sm">
                                  <div className="flex items-center space-x-2 w-full">
                                    <Key className="h-3 w-3 flex-shrink-0" />
                                    <span className="truncate">
                                      {getApiKeyDisplayText()}
                                    </span>
                                  </div>
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="default">
                                    <div className="flex items-center space-x-2">
                                      <Key className="h-3 w-3" />
                                      <span>使用默认密钥</span>
                                    </div>
                                  </SelectItem>
                                  {apiKeys.map((key) => (
                                    <SelectItem key={key.id} value={String(key.id)}>
                                      <div className="flex items-center space-x-2">
                                        <Key className="h-3 w-3" />
                                        <span>{key.name}</span>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <p className="text-xs text-muted-foreground">
                              选择用于此次测试的AI密钥，默认使用系统配置
                            </p>
                          </div>

                          {/* Reset Button */}
                          <div className="flex justify-end pt-2 border-t">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setAiOverride({
                                enabled: false,
                                provider: "openai",
                                model: "",
                                temperature: 0.7,
                                maxTokens: 2000,
                                baseUrl: undefined,
                                customProviderName: undefined,
                                apiKeyId: undefined
                              })}
                            >
                              <RotateCcw className="h-3 w-3 mr-1" />
                              重置为默认
                            </Button>
                          </div>
                        </div>
                      </CollapsibleContent>
                    </Collapsible>

                    {/* Submit Button */}
                    <div className="flex gap-2">
                      <LoadingButton
                        type="submit"
                        isLoading={isLoading}
                        disabled={!input.trim() || input.length < 5}
                        loadingText="测试进行中..."
                        className="flex-1 min-h-[44px]"
                      >
                        {isLoading ? (
                          <>
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                            测试进行中...
                          </>
                        ) : (
                          <>
                            <Play className="h-4 w-4 mr-2" />
                            开始测试
                          </>
                        )}
                      </LoadingButton>

                      <Button
                        variant="outline"
                        onClick={handleClearInput}
                        disabled={isLoading}
                        className="px-4"
                        size="lg"
                      >
                        <RotateCcw className="h-4 w-4" />
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>

          {/* Execution Tab Content */}
          <TabsContent value="execution" className="w-full space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="w-full space-y-6"
            >
              {executionState.isRunning || executionState.hasResults ? (
                <div className="space-y-6">
                  {/* Workflow Steps Progress (if available) - Compact horizontal layout */}
                  {workflowSteps.length > 0 && (
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="flex items-center space-x-2 text-base">
                          <Users className="h-4 w-4 text-primary" />
                          <span>工作流程步骤</span>
                        </CardTitle>
                        <CardDescription className="text-xs">
                          团队成员的具体工作步骤和进度
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pt-0">
                        {/* Compact horizontal layout for workflow steps */}
                        <div className="flex flex-col sm:flex-row gap-2 overflow-x-auto pb-1">
                          {workflowSteps.map((step, index) => (
                            <div
                              key={`workflow-step-${index}-${step.name || index}`}
                              className={`flex-shrink-0 flex flex-col items-center space-y-1 p-2 rounded-md border min-w-[120px] ${
                                executionState.completedSteps.includes(step.name || `step-${index}`)
                                  ? 'bg-green-50 border-green-200 dark:bg-green-950/30 dark:border-green-800/50'
                                  : executionState.currentStep === (step.name || `step-${index}`)
                                  ? 'bg-blue-50 border-blue-200 dark:bg-blue-950/30 dark:border-blue-800/50'
                                  : 'bg-muted/30 border-border'
                              }`}
                            >
                              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                                executionState.completedSteps.includes(step.name || `step-${index}`)
                                  ? 'bg-green-500 text-white'
                                  : executionState.currentStep === (step.name || `step-${index}`)
                                  ? 'bg-blue-500 text-white animate-pulse'
                                  : 'bg-muted text-muted-foreground'
                              }`}>
                                {executionState.completedSteps.includes(step.name || `step-${index}`) ? '✓' : index + 1}
                              </div>
                              <div className="text-center">
                                <h4 className="font-medium text-xs leading-tight">{step.name}</h4>
                                <p className="text-[10px] text-muted-foreground leading-tight">{step.assignee}</p>
                              </div>
                              {executionState.currentStep === (step.name || `step-${index}`) && (
                                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse"></div>
                              )}
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Enhanced Variable Tracking */}
                  <EnhancedVariableTracking
                    variablePlaceholders={variablePlaceholders}
                    websocketStatus={websocketStatus}
                    isExecuting={executionState.isRunning}
                  />

                  {/* Real-time Execution Output - Moved down */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Bot className="h-5 w-5 text-primary" />
                        <span>实时执行输出</span>
                        {executionState.isRunning && (
                          <Badge variant="secondary" className="animate-pulse">
                            执行中
                          </Badge>
                        )}
                      </CardTitle>
                      <CardDescription>
                        AI团队的实时执行过程和输出结果
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="border rounded-lg p-4 bg-muted/50 min-h-[300px] max-h-[600px] overflow-y-auto">
                        {isLoading && !realtimeResponse && (
                          <div className="flex items-center space-x-2 text-muted-foreground">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                            <span>Agent正在处理请求...</span>
                          </div>
                        )}

                        {realtimeResponse && (
                          <div className="space-y-2">
                            <StreamingMarkdownRenderer
                              content={realtimeResponse}
                              isStreaming={showProgress}
                              className="text-sm"
                            />
                          </div>
                        )}

                        {!realtimeResponse && !isLoading && (
                          <div className="text-center py-8 text-muted-foreground">
                            <div className="text-4xl mb-2">📝</div>
                            <p>执行输出将显示在这里</p>
                            <p className="text-xs mt-1">提交测试后，您将看到AI团队的实时工作过程</p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="text-center py-12">
                    <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                      <Zap className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-medium mb-2">等待执行</h3>
                    <p className="text-muted-foreground">
                      请先在测试页面提交任务以开始执行
                    </p>
                  </CardContent>
                </Card>
              )}
            </motion.div>
          </TabsContent>

          {/* Results Tab Content */}
          <TabsContent value="results" className="w-full space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="w-full space-y-6"
            >
              {executionState.hasResults ? (
                <div className="max-w-4xl mx-auto space-y-4">
                  {/* Compact Success Header */}
                  <motion.div
                    className="text-center space-y-2"
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6 }}
                  >
                    <motion.div
                      className="flex justify-center"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.2, type: "spring", stiffness: 200 }}
                    >
                      <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                        <CheckCircle className="h-6 w-6 text-green-500" />
                      </div>
                    </motion.div>
                    <div className="space-y-1">
                      <h2 className="text-lg font-bold text-green-700 dark:text-green-300">
                        测试完成！
                      </h2>
                      <p className="text-sm text-muted-foreground">
                        AI团队已成功完成任务，以下是执行结果
                      </p>
                    </div>
                  </motion.div>



                  {/* Test Input */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                  >
                    <Card>
                      <CardHeader>
                        <CardTitle>测试输入</CardTitle>
                        <CardDescription>
                          您提交给AI团队的原始任务
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="bg-muted/50 rounded-lg p-4">
                          <p className="text-sm">{input}</p>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>

                  {/* Step-by-Step Execution Results */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.5 }}
                  >
                    <Card>
                      <CardHeader>
                        <CardTitle>执行结果</CardTitle>
                        <CardDescription>
                          AI团队协作生成的详细步骤输出
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        {(() => {
                          console.log("🔍 [RESULTS DISPLAY DEBUG] Rendering execution results");
                          console.log("🔍 [RESULTS DISPLAY DEBUG] ExecutionStages:", executionStages);
                          console.log("🔍 [RESULTS DISPLAY DEBUG] ExecutionStages length:", executionStages.length);
                          return null;
                        })()}
                        {executionStages.length > 0 ? (
                          <div className="space-y-4 sm:space-y-6">
                            {executionStages.map((stage, index) => (
                              <motion.div
                                key={`stage-${index}`}
                                className="border rounded-lg p-3 sm:p-4 bg-muted/30 hover:bg-muted/40 transition-colors"
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.3, delay: index * 0.1 }}
                              >
                                <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 mb-3 sm:mb-4">
                                  <div className="flex items-center space-x-3">
                                    <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium flex-shrink-0">
                                      {index + 1}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                      <h4 className="font-medium text-sm sm:text-base truncate">
                                        {stage.step_name || stage.name || `步骤 ${index + 1}`}
                                      </h4>
                                      {stage.assignee && (
                                        <p className="text-xs sm:text-sm text-muted-foreground truncate">
                                          负责人: {stage.assignee}
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="self-start sm:self-center"
                                    onClick={() => {
                                      const content = typeof stage.output === 'string' ? stage.output : JSON.stringify(stage.output, null, 2);
                                      navigator.clipboard.writeText(content);
                                    }}
                                  >
                                    <Copy className="h-4 w-4" />
                                    <span className="ml-1 hidden sm:inline">复制</span>
                                  </Button>
                                </div>
                                <div className="bg-background border rounded-lg p-3 sm:p-4 shadow-sm">
                                  {stage.output ? (
                                    <JsonFormatter
                                      content={typeof stage.output === 'string' ? stage.output : JSON.stringify(stage.output, null, 2)}
                                      isStreaming={false}
                                      className="text-xs sm:text-sm"
                                      maxHeight="max-h-64 sm:max-h-80"
                                    />
                                  ) : (
                                    <div className="text-center py-6 sm:py-8 text-muted-foreground">
                                      <div className="text-2xl sm:text-3xl mb-2">📝</div>
                                      <p className="text-xs sm:text-sm">此步骤暂无输出内容</p>
                                    </div>
                                  )}
                                </div>
                              </motion.div>
                            ))}
                          </div>
                        ) : response ? (
                          <div className="space-y-4">
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                              <span>显示合并的执行结果（未检测到步骤分解）</span>
                            </div>
                            <div className="bg-background border rounded-lg p-3 sm:p-4 max-h-80 sm:max-h-96 overflow-y-auto shadow-sm">
                              <JsonFormatter
                                content={response}
                                isStreaming={false}
                                className="text-xs sm:text-sm"
                              />
                            </div>
                          </div>
                        ) : (
                          <div className="text-center py-12 sm:py-16 text-muted-foreground">
                            <div className="text-4xl sm:text-5xl mb-4">📄</div>
                            <h3 className="text-base sm:text-lg font-medium mb-2">暂无结果数据</h3>
                            <p className="text-xs sm:text-sm max-w-md mx-auto">
                              完成测试执行后，详细的步骤结果将显示在这里。每个步骤的输出将单独展示，支持JSON格式化和语法高亮。
                            </p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>

                  {/* Individual Agent Outputs */}
                  {individualAgentOutputs.length > 0 && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.6 }}
                    >
                      <IndividualAgentOutputs
                        individualAgentOutputs={individualAgentOutputs}
                        agentExecutionSequence={agentExecutionSequence}
                      />
                    </motion.div>
                  )}

                  {/* Unified Execution Metadata */}
                  {responseMetadata && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.3 }}
                    >
                      <Card className="border-green-200 dark:border-green-800">
                        <CardHeader className="pb-3">
                          <CardTitle className="flex items-center space-x-2 text-base">
                            <Zap className="h-4 w-4 text-primary" />
                            <span>执行元数据</span>
                          </CardTitle>
                          <CardDescription className="text-xs">
                            详细的执行信息和性能指标
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="pt-0 space-y-6">
                          {/* Key Metrics Grid */}
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
                            <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800/50 transition-all hover:shadow-sm">
                              <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400 mb-1" />
                              <div className="text-lg font-bold text-blue-900 dark:text-blue-100">
                                {responseMetadata.responseTime ? `${responseMetadata.responseTime}ms` : '未知'}
                              </div>
                              <div className="text-xs font-medium text-blue-700 dark:text-blue-300">执行时间</div>
                            </div>
                            <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800/50 transition-all hover:shadow-sm">
                              <Zap className="h-4 w-4 text-purple-600 dark:text-purple-400 mb-1" />
                              <div className="text-lg font-bold text-purple-900 dark:text-purple-100">
                                {responseMetadata.tokensUsed ? responseMetadata.tokensUsed.toLocaleString() : '0'}
                              </div>
                              <div className="text-xs font-medium text-purple-700 dark:text-purple-300">Token使用</div>
                            </div>
                            <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800/50 transition-all hover:shadow-sm">
                              <DollarSign className="h-4 w-4 text-orange-600 dark:text-orange-400 mb-1" />
                              <div className="text-lg font-bold text-orange-900 dark:text-orange-100">
                                估算中
                              </div>
                              <div className="text-xs font-medium text-orange-700 dark:text-orange-300">估算成本</div>
                            </div>
                            <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/20 rounded-lg border border-green-200 dark:border-green-800/50 transition-all hover:shadow-sm">
                              {responseMetadata.status === 'completed' ? (
                                <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400 mb-1" />
                              ) : (
                                <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400 mb-1" />
                              )}
                              <div className={`text-lg font-bold ${responseMetadata.status === 'completed' ? 'text-green-900 dark:text-green-100' : 'text-red-900 dark:text-red-100'}`}>
                                {responseMetadata.status === 'completed' ? '成功' : responseMetadata.status || '未知'}
                              </div>
                              <div className={`text-xs font-medium ${responseMetadata.status === 'completed' ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'}`}>执行状态</div>
                            </div>
                          </div>

                          {/* Detailed Configuration Metadata */}
                          <div className="space-y-4">
                            <div className="flex items-center space-x-2 pb-2 border-b border-border/50">
                              <Settings className="h-4 w-4 text-muted-foreground" />
                              <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">配置详情</h4>
                            </div>
                            {/* AI Model Configuration Section */}
                            <div className="space-y-3">
                              <div className="flex items-center space-x-2 pb-1">
                                <Cpu className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                                <h5 className="text-xs font-semibold text-blue-700 dark:text-blue-300 uppercase tracking-wide">AI模型配置</h5>
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                                {/* Model Used */}
                                <div className="p-3 bg-blue-50/50 dark:bg-blue-950/20 rounded-lg border border-blue-200/50 dark:border-blue-800/30">
                                  <Label className="text-xs font-semibold text-blue-700 dark:text-blue-300 uppercase tracking-wide">使用模型</Label>
                                  <div className="text-sm font-medium mt-1 text-blue-900 dark:text-blue-100">
                                    {responseMetadata.modelUsed || '默认模型'}
                                  </div>
                                </div>

                                {/* Provider Used */}
                                <div className="p-3 bg-blue-50/50 dark:bg-blue-950/20 rounded-lg border border-blue-200/50 dark:border-blue-800/30">
                                  <Label className="text-xs font-semibold text-blue-700 dark:text-blue-300 uppercase tracking-wide">AI提供商</Label>
                                  <div className="text-sm font-medium mt-1 text-blue-900 dark:text-blue-100">
                                    {responseMetadata.providerUsed || '默认提供商'}
                                  </div>
                                </div>

                                {/* Temperature */}
                                <div className="p-3 bg-blue-50/50 dark:bg-blue-950/20 rounded-lg border border-blue-200/50 dark:border-blue-800/30">
                                  <Label className="text-xs font-semibold text-blue-700 dark:text-blue-300 uppercase tracking-wide">温度参数</Label>
                                  <div className="text-sm font-medium mt-1 text-blue-900 dark:text-blue-100">
                                    {responseMetadata.temperatureUsed !== undefined ? responseMetadata.temperatureUsed : '0.7'}
                                  </div>
                                </div>

                                {/* Max Tokens */}
                                <div className="p-3 bg-blue-50/50 dark:bg-blue-950/20 rounded-lg border border-blue-200/50 dark:border-blue-800/30">
                                  <Label className="text-xs font-semibold text-blue-700 dark:text-blue-300 uppercase tracking-wide">最大令牌数</Label>
                                  <div className="text-sm font-medium mt-1 text-blue-900 dark:text-blue-100">
                                    {responseMetadata.maxTokensUsed || '2000'}
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Connection Configuration Section */}
                            <div className="space-y-3">
                              <div className="flex items-center space-x-2 pb-1">
                                <Globe className="h-3 w-3 text-green-600 dark:text-green-400" />
                                <h5 className="text-xs font-semibold text-green-700 dark:text-green-300 uppercase tracking-wide">连接配置</h5>
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                {/* Base URL */}
                                <div className="p-3 bg-green-50/50 dark:bg-green-950/20 rounded-lg border border-green-200/50 dark:border-green-800/30">
                                  <Label className="text-xs font-semibold text-green-700 dark:text-green-300 uppercase tracking-wide">API端点</Label>
                                  <div className="text-sm font-medium mt-1 text-green-900 dark:text-green-100 break-all">
                                    {responseMetadata.baseUrlUsed || '默认端点'}
                                  </div>
                                </div>

                                {/* Custom Provider Name */}
                                <div className="p-3 bg-green-50/50 dark:bg-green-950/20 rounded-lg border border-green-200/50 dark:border-green-800/30">
                                  <Label className="text-xs font-semibold text-green-700 dark:text-green-300 uppercase tracking-wide">自定义提供商</Label>
                                  <div className="text-sm font-medium mt-1 text-green-900 dark:text-green-100">
                                    {responseMetadata.customProviderNameUsed || '无'}
                                  </div>
                                </div>

                                {/* API Key Used */}
                                <div className="p-3 bg-green-50/50 dark:bg-green-950/20 rounded-lg border border-green-200/50 dark:border-green-800/30">
                                  <Label className="text-xs font-semibold text-green-700 dark:text-green-300 uppercase tracking-wide">AI密钥</Label>
                                  <div className="text-sm font-medium mt-1 text-green-900 dark:text-green-100 flex items-center gap-1">
                                    <Key className="h-3 w-3" />
                                    {responseMetadata.apiKeyUsed || '默认密钥'}
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Execution Configuration Section */}
                            <div className="space-y-3">
                              <div className="flex items-center space-x-2 pb-1">
                                <Play className="h-3 w-3 text-purple-600 dark:text-purple-400" />
                                <h5 className="text-xs font-semibold text-purple-700 dark:text-purple-300 uppercase tracking-wide">执行配置</h5>
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                {/* Execution Method */}
                                <div className="p-3 bg-purple-50/50 dark:bg-purple-950/20 rounded-lg border border-purple-200/50 dark:border-purple-800/30">
                                  <Label className="text-xs font-semibold text-purple-700 dark:text-purple-300 uppercase tracking-wide">执行方式</Label>
                                  <div className="text-sm font-medium mt-1 text-purple-900 dark:text-purple-100 flex items-center gap-1">
                                    {responseMetadata.executionMethod === 'streaming' ? (
                                      <>
                                        <Zap className="h-3 w-3" />
                                        流式执行
                                      </>
                                    ) : (
                                      <>
                                        <Square className="h-3 w-3" />
                                        标准执行
                                      </>
                                    )}
                                  </div>
                                </div>

                                {/* Stream Mode */}
                                <div className="p-3 bg-purple-50/50 dark:bg-purple-950/20 rounded-lg border border-purple-200/50 dark:border-purple-800/30">
                                  <Label className="text-xs font-semibold text-purple-700 dark:text-purple-300 uppercase tracking-wide">流式模式</Label>
                                  <div className="text-sm font-medium mt-1 text-purple-900 dark:text-purple-100">
                                    {responseMetadata.streamModeUsed ? '启用' : '禁用'}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                        {/* Token Breakdown */}
                        {(responseMetadata.inputTokens || responseMetadata.outputTokens) && (
                          <div className="space-y-4">
                            <div className="flex items-center space-x-2 pb-2 border-b border-border/50">
                              <Zap className="h-4 w-4 text-muted-foreground" />
                              <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">令牌使用详情</h4>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800/50">
                                <div>
                                  <Label className="text-xs font-semibold text-blue-700 dark:text-blue-300 uppercase tracking-wide">输入令牌</Label>
                                  <div className="text-lg font-bold text-blue-900 dark:text-blue-100 mt-1">
                                    {(responseMetadata.inputTokens || 0).toLocaleString()}
                                  </div>
                                </div>
                                <div className="w-8 h-8 bg-blue-200 dark:bg-blue-800/50 rounded-full flex items-center justify-center">
                                  <ArrowRight className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                </div>
                              </div>
                              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/20 rounded-lg border border-green-200 dark:border-green-800/50">
                                <div>
                                  <Label className="text-xs font-semibold text-green-700 dark:text-green-300 uppercase tracking-wide">输出令牌</Label>
                                  <div className="text-lg font-bold text-green-900 dark:text-green-100 mt-1">
                                    {(responseMetadata.outputTokens || 0).toLocaleString()}
                                  </div>
                                </div>
                                <div className="w-8 h-8 bg-green-200 dark:bg-green-800/50 rounded-full flex items-center justify-center">
                                  <ArrowLeft className="h-4 w-4 text-green-600 dark:text-green-400" />
                                </div>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Error Information */}
                        {responseMetadata.error && (
                          <div className="space-y-4">
                            <div className="flex items-center space-x-2 pb-2 border-b border-border/50">
                              <AlertCircle className="h-4 w-4 text-muted-foreground" />
                              <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">错误信息</h4>
                            </div>
                            <Alert variant="destructive" className="border-red-200 dark:border-red-800/50 bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/20">
                              <AlertCircle className="h-5 w-5" />
                              <AlertDescription className="text-sm font-medium">
                                执行过程中发生错误: {responseMetadata.errorMessage || '未知错误'}
                              </AlertDescription>
                            </Alert>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                    </motion.div>
                  )}

                  {/* Variable Resolution Summary */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                  >
                    <Card className="border-blue-200 dark:border-blue-800">
                      <CardHeader className="pb-3">
                        <CardTitle className="flex items-center space-x-2 text-base">
                          <Settings className="h-4 w-4 text-primary" />
                          <span>变量解析摘要</span>
                          <Badge variant="secondary" className="text-xs">
                            {variablePlaceholders.filter(p => p.value).length}/{variablePlaceholders.length} 已解析
                          </Badge>
                        </CardTitle>
                        <CardDescription className="text-xs">
                          执行过程中团队变量占位符的最终解析状态
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pt-0 space-y-4">
                        {variablePlaceholders.length > 0 ? (
                          <>
                            {/* Summary Statistics */}
                            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                              <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/20 rounded-lg border border-green-200 dark:border-green-800/50">
                                <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400 mb-1" />
                                <div className="text-lg font-bold text-green-900 dark:text-green-100">
                                  {variablePlaceholders.filter(p => p.value).length}
                                </div>
                                <div className="text-xs font-medium text-green-700 dark:text-green-300">已解析</div>
                              </div>
                              <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800/50">
                                <Clock className="h-4 w-4 text-yellow-600 dark:text-yellow-400 mb-1" />
                                <div className="text-lg font-bold text-yellow-900 dark:text-yellow-100">
                                  {variablePlaceholders.filter(p => !p.value).length}
                                </div>
                                <div className="text-xs font-medium text-yellow-700 dark:text-yellow-300">未解析</div>
                              </div>
                              <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800/50">
                                <Settings className="h-4 w-4 text-blue-600 dark:text-blue-400 mb-1" />
                                <div className="text-lg font-bold text-blue-900 dark:text-blue-100">
                                  {variablePlaceholders.length}
                                </div>
                                <div className="text-xs font-medium text-blue-700 dark:text-blue-300">总计</div>
                              </div>
                            </div>
                          {/* Enhanced Summary Statistics with Inter-Agent Communication */}
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
                            <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/20 rounded-lg border border-green-200 dark:border-green-800/50">
                              <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400 mb-1" />
                              <div className="text-lg font-bold text-green-900 dark:text-green-100">
                                {variablePlaceholders.filter(p => p.value).length}
                              </div>
                              <div className="text-xs font-medium text-green-700 dark:text-green-300">已解析</div>
                            </div>
                            <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800/50">
                              <Clock className="h-4 w-4 text-yellow-600 dark:text-yellow-400 mb-1" />
                              <div className="text-lg font-bold text-yellow-900 dark:text-yellow-100">
                                {variablePlaceholders.filter(p => !p.value).length}
                              </div>
                              <div className="text-xs font-medium text-yellow-700 dark:text-yellow-300">未解析</div>
                            </div>
                            <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800/50">
                              <ArrowRightLeft className="h-4 w-4 text-purple-600 dark:text-purple-400 mb-1" />
                              <div className="text-lg font-bold text-purple-900 dark:text-purple-100">
                                {variablePlaceholders.filter(p => p.communicationType === 'inter-agent').length}
                              </div>
                              <div className="text-xs font-medium text-purple-700 dark:text-purple-300">代理间通信</div>
                            </div>
                            <div className="flex flex-col items-center justify-center p-3 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800/50">
                              <Settings className="h-4 w-4 text-blue-600 dark:text-blue-400 mb-1" />
                              <div className="text-lg font-bold text-blue-900 dark:text-blue-100">
                                {variablePlaceholders.length}
                              </div>
                              <div className="text-xs font-medium text-blue-700 dark:text-blue-300">总计</div>
                            </div>
                          </div>

                          {/* Variable Details */}
                          <div className="space-y-3">
                            <div className="flex items-center space-x-2 pb-2 border-b border-border/50">
                              <Settings className="h-4 w-4 text-muted-foreground" />
                              <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">变量详情</h4>
                            </div>
                            <div className="grid grid-cols-1 gap-3">
                              {variablePlaceholders
                                .sort((a, b) => a.stepIndex - b.stepIndex)
                                .map((placeholder) => (
                                <div
                                  key={placeholder.id}
                                  className={`p-3 rounded-lg border ${
                                    placeholder.value
                                      ? 'bg-green-50/50 border-green-200/50 dark:bg-green-950/20 dark:border-green-800/30'
                                      : 'bg-yellow-50/50 border-yellow-200/50 dark:bg-yellow-950/20 dark:border-yellow-800/30'
                                  }`}
                                >
                                  <div className="flex items-start justify-between gap-3">
                                    <div className="flex-1 min-w-0">
                                      <div className="flex items-center gap-2 mb-1">
                                        <code className="text-sm font-mono bg-background/70 px-2 py-1 rounded border">
                                          {placeholder.placeholderName}
                                        </code>
                                        <Badge variant="outline" className="text-xs">
                                          {placeholder.sourceAgent}
                                        </Badge>
                                        {/* Communication type badge */}
                                        <Badge
                                          variant={
                                            placeholder.communicationType === 'inter-agent' ? 'default' :
                                            placeholder.communicationType === 'user-input' ? 'secondary' :
                                            placeholder.communicationType === 'system' ? 'outline' : 'destructive'
                                          }
                                          className="text-xs"
                                        >
                                          {placeholder.communicationType === 'inter-agent' ? '🔄' :
                                           placeholder.communicationType === 'user-input' ? '👤' :
                                           placeholder.communicationType === 'system' ? '⚙️' : '🔧'}
                                        </Badge>
                                      </div>
                                      <div className="text-xs text-muted-foreground mb-2 space-y-1">
                                        {placeholder.semanticDescription && (
                                          <div>{placeholder.semanticDescription}</div>
                                        )}
                                        {/* Inter-agent communication details */}
                                        {placeholder.isSharedBetweenAgents && (
                                          <div className="space-y-1 p-2 bg-purple-50/50 dark:bg-purple-950/20 rounded border border-purple-200/50 dark:border-purple-800/30">
                                            <div className="font-medium text-purple-700 dark:text-purple-300 text-xs">
                                              🔄 代理间通信详情
                                            </div>
                                            {placeholder.destinationAgents.length > 0 && (
                                              <div>
                                                <span className="font-medium">目标代理:</span> {placeholder.destinationAgents.join(', ')}
                                              </div>
                                            )}
                                            {placeholder.dependsOn.length > 0 && (
                                              <div>
                                                <span className="font-medium">依赖变量:</span> {placeholder.dependsOn.join(', ')}
                                              </div>
                                            )}
                                            {placeholder.dataFlowChain.length > 1 && (
                                              <div>
                                                <span className="font-medium">数据流链:</span> {
                                                  placeholder.dataFlowChain.map(flow => flow.agent).join(' → ')
                                                }
                                              </div>
                                            )}
                                            {placeholder.contextDependencies.length > 0 && (
                                              <div>
                                                <span className="font-medium">上下文依赖:</span> {placeholder.contextDependencies.join(', ')}
                                              </div>
                                            )}
                                          </div>
                                        )}
                                      </div>
                                      {placeholder.value ? (
                                        <div className="space-y-1">
                                          <div className="text-xs font-medium text-green-700 dark:text-green-300">
                                            解析值:
                                          </div>
                                          <div className="text-sm bg-background/70 p-3 rounded border max-h-32 overflow-y-auto leading-relaxed">
                                            <pre className="whitespace-pre-wrap break-words font-sans text-sm text-foreground">
                                              {placeholder.value}
                                            </pre>
                                          </div>
                                        </div>
                                      ) : (
                                        <div className="text-xs text-yellow-700 dark:text-yellow-300 italic">
                                          未在执行过程中解析
                                        </div>
                                      )}
                                    </div>
                                    <div className="flex-shrink-0">
                                      {placeholder.value ? (
                                        <CheckCircle className="h-4 w-4 text-green-500" />
                                      ) : (
                                        <XCircle className="h-4 w-4 text-yellow-500" />
                                      )}
                                    </div>
                                  </div>
                                  {placeholder.resolvedAt && (
                                    <div className="text-xs text-muted-foreground mt-2 pt-2 border-t border-border/50">
                                      解析于: {new Date(placeholder.resolvedAt).toLocaleString('zh-CN')}
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        </>
                        ) : (
                          <div className="text-center py-8 text-muted-foreground">
                            <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                              <Settings className="h-6 w-6 text-muted-foreground" />
                            </div>
                            <h3 className="text-sm font-medium mb-2">未检测到变量</h3>
                            <p className="text-xs">
                              此次执行过程中未发现团队变量占位符的使用
                            </p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>

                  {/* Action Buttons */}
                  <motion.div
                    className="flex flex-col sm:flex-row justify-center gap-4 pt-6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.6 }}
                  >
                    <Button
                      onClick={() => {
                        setMainActiveTab("test");
                        setInput("");
                      }}
                      className="min-h-[44px]"
                    >
                      <RotateCcw className="h-4 w-4 mr-2" />
                      新建测试
                    </Button>

                    <Button variant="outline" className="min-h-[44px]">
                      <Share className="h-4 w-4 mr-2" />
                      分享结果
                    </Button>

                    <Button
                      variant="outline"
                      onClick={() => setMainActiveTab("history")}
                      className="min-h-[44px]"
                    >
                      <History className="h-4 w-4 mr-2" />
                      查看历史
                    </Button>
                  </motion.div>
                </div>
              ) : (
                <Card>
                  <CardContent className="text-center py-12">
                    <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                      <Settings className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-medium mb-2">暂无结果</h3>
                    <p className="text-muted-foreground">
                      完成测试执行后，结果将显示在这里
                    </p>
                  </CardContent>
                </Card>
              )}
            </motion.div>
          </TabsContent>

          {/* History Tab Content */}
          <TabsContent value="history" className="w-full space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="w-full space-y-6"
            >
              {/* Header */}
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                  <h2 className="text-2xl font-bold flex items-center gap-2">
                    <History className="h-6 w-6 text-primary" />
                    测试历史
                  </h2>
                  <p className="text-muted-foreground mt-1">
                    当前代理的最新测试记录和执行历史
                    {recentTests.length > 0 && (
                      <span className="ml-2 text-sm bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full">
                        共 {recentTests.length} 条记录
                      </span>
                    )}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => loadRecentTests()}
                    disabled={loadingRecentTests}
                    className="flex items-center gap-2"
                  >
                    <RefreshCw className={`w-4 h-4 ${loadingRecentTests ? "animate-spin" : ""}`} />
                    刷新
                  </Button>
                </div>
              </div>

              {/* Loading State */}
              {loadingRecentTests && recentTests.length === 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-3">
                  {Array.from({ length: 6 }).map((_, index) => (
                    <Card key={index} className="h-fit">
                      <CardContent className="p-3">
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Skeleton className="w-6 h-6 rounded-md" />
                              <Skeleton className="w-12 h-4" />
                            </div>
                            <Skeleton className="w-12 h-3" />
                          </div>
                          <Skeleton className="w-16 h-3" />
                          <Skeleton className="w-full h-3" />
                          <Skeleton className="w-3/4 h-3" />
                          <Skeleton className="w-20 h-3" />
                          <div className="flex items-center justify-between pt-2 border-t border-border">
                            <Skeleton className="w-16 h-5" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : recentTests.length === 0 ? (
                <div className="text-center py-12">
                  <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
                    <History className="w-12 h-12 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">暂无测试记录</h3>
                  <p className="text-muted-foreground mb-6">
                    完成第一次测试后，记录将显示在这里
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => setMainActiveTab("test")}
                  >
                    <Play className="h-4 w-4 mr-2" />
                    开始第一次测试
                  </Button>
                </div>
              ) : (
                <>
                  {/* Test History Cards */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-3">
                    {recentTests.map((test) => {
                      const statusConfig = getHistoryStatusConfig(test.status);
                      const StatusIcon = statusConfig.icon;

                      return (
                        <Card
                          key={test.id}
                          className={`test-card-compact ${statusConfig.borderColor} h-fit hover:shadow-md transition-shadow`}
                        >
                          <CardContent className="p-3 test-card-mobile">
                            {/* Header - Compact */}
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2 min-w-0 flex-1">
                                <div className={`p-1.5 rounded-md ${statusConfig.bgColor} shrink-0`}>
                                  <StatusIcon className={`w-3.5 h-3.5 ${statusConfig.color}`} />
                                </div>
                                <div className="min-w-0 flex-1">
                                  <Badge className={`${statusConfig.bgColor} ${statusConfig.color} border-0 text-xs px-2 py-0.5`}>
                                    {statusConfig.label}
                                  </Badge>
                                </div>
                              </div>
                              {test.execution_duration_ms && (
                                <div className="flex items-center gap-1 text-xs text-muted-foreground shrink-0">
                                  <Timer className="w-3 h-3" />
                                  <span className="hidden sm:inline">{formatHistoryDuration(test.execution_duration_ms)}</span>
                                </div>
                              )}
                            </div>

                            {/* Timestamp - Compact */}
                            <div className="text-xs text-muted-foreground mb-2">
                              {format(new Date(test.started_at), 'MM-dd HH:mm')}
                            </div>

                            {/* Input Text - Truncated */}
                            <div className="mb-2">
                              <p className="text-xs leading-relaxed text-foreground line-clamp-2">
                                {truncateHistoryText(test.input_text, 120)}
                              </p>
                            </div>

                            {/* Metadata Row - Compact */}
                            <div className="flex items-center gap-1 mb-2 text-xs">
                              <span className="text-muted-foreground truncate">
                                {agent.agent_id}
                              </span>
                              <span className="text-muted-foreground">•</span>
                              <span className="text-muted-foreground">
                                {test.id ? String(test.id).slice(-6) : 'N/A'}
                              </span>
                            </div>

                            {/* Action Buttons - Compact */}
                            <div className="flex items-center justify-between mt-2 pt-2 border-t border-border">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => loadTestDetail(test.test_id || String(test.id))}
                                className="h-6 px-2 text-xs"
                              >
                                <Eye className="w-3 h-3 mr-1" />
                                查看
                              </Button>
                              {test.status === 'completed' && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    // Set input text
                                    setInput(test.input_text);

                                    // Restore comprehensive configuration if available
                                    if (test.ai_config_override || test.api_key_id) {
                                      const config = test.ai_config_override || {};

                                      // Restore AI configuration
                                      setAiOverride({
                                        enabled: Boolean(config.advanced_config_enabled || config.model || config.provider || config.temperature !== undefined || config.max_tokens || config.base_url || test.api_key_id),
                                        provider: config.provider || "openai",
                                        model: config.model || "",
                                        temperature: config.temperature !== undefined ? config.temperature : 0.7,
                                        maxTokens: config.max_tokens || 2000,
                                        baseUrl: config.base_url || undefined,
                                        customProviderName: config.custom_provider_name || undefined,
                                        apiKeyId: test.api_key_id ? String(test.api_key_id) : (config.api_key_id ? String(config.api_key_id) : undefined)
                                      });

                                      // Restore execution options
                                      setOptions({
                                        stream: config.stream_mode !== undefined ? config.stream_mode : true
                                      });
                                    } else {
                                      // Reset to default if no config was saved
                                      setAiOverride({
                                        enabled: false,
                                        provider: "openai",
                                        model: "",
                                        temperature: 0.7,
                                        maxTokens: 2000,
                                        baseUrl: undefined,
                                        customProviderName: undefined,
                                        apiKeyId: undefined
                                      });

                                      // Reset execution options to default
                                      setOptions({
                                        stream: true
                                      });
                                    }

                                    // Switch to test tab
                                    setMainActiveTab("test");
                                  }}
                                  className="h-6 px-2 text-xs"
                                >
                                  <RotateCcw className="w-3 h-3 mr-1" />
                                  重新运行
                                </Button>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>

                  {/* Load More Button */}
                  <div className="text-center pt-4">
                    <Button
                      variant="outline"
                      onClick={() => loadRecentTests()}
                      disabled={loadingRecentTests}
                    >
                      {loadingRecentTests ? (
                        <>
                          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                          加载中...
                        </>
                      ) : (
                        <>
                          <History className="h-4 w-4 mr-2" />
                          刷新历史记录
                        </>
                      )}
                    </Button>
                  </div>
                </>
              )}
            </motion.div>
          </TabsContent>
        </Tabs>
      </motion.div>

      {/* Test Detail Dialog */}
      <UnifiedTestDetailDialog
        open={showDetailDialog}
        onOpenChange={setShowDetailDialog}
        testDetail={selectedTest}
        title="测试详情"
      />
    </div>
  );
}
