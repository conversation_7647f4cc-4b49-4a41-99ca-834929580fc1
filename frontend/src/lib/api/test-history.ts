/**
 * Test history API client
 */

import { apiClient } from '../api-client';

export interface TestHistoryCreate {
  test_id: string;
  agent_id: string;
  input_text: string;
  ai_config_override?: Record<string, any> | null;
  api_key_id?: number | null;
  api_key_name?: string | null;
  input_metadata?: Record<string, any> | null;
}

export interface TestHistoryUpdate {
  status?: 'running' | 'completed' | 'failed' | 'cancelled';
  completed_at?: string;
  execution_duration_ms?: number;
  execution_stages?: Array<Record<string, any>>;
  progress_updates?: Array<Record<string, any>>;
  final_output?: string;
  response_metadata?: Record<string, any>;
  context_summary?: Record<string, any>;
  context_placeholders_used?: Array<Record<string, any>>;
  team_member_interactions?: Array<Record<string, any>>;
  error_message?: string;
  error_details?: Record<string, any>;
}

export interface TestHistoryResponse {
  id: number;
  test_id: string;
  user_id: number;
  agent_id: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  started_at: string;
  completed_at?: string;
  execution_duration_ms?: number;
  ai_config_override?: Record<string, any>;
  api_key_id?: number;
  api_key_name?: string;
  input_text: string;
  final_output?: string;
  error_message?: string;
  created_at: string;
}

export interface TestHistoryDetailResponse extends TestHistoryResponse {
  execution_stages?: Array<Record<string, any>>;
  progress_updates?: Array<Record<string, any>>;
  response_metadata?: Record<string, any>;
  context_summary?: Record<string, any>;
  context_placeholders_used?: Array<Record<string, any>>;
  team_member_interactions?: Array<Record<string, any>>;
  individual_agent_outputs?: Array<Record<string, any>>;
  agent_execution_sequence?: Array<Record<string, any>>;
  inter_agent_variables?: Record<string, any>;
  agent_performance_metrics?: Record<string, any>;
  error_details?: Record<string, any>;
  input_metadata?: Record<string, any>;
}

export interface TestHistoryListResponse {
  tests: TestHistoryResponse[];
  total: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface TestHistoryListParams {
  page?: number;
  limit?: number;
  agent_id?: string;
  status?: 'running' | 'completed' | 'failed' | 'cancelled';
}

export const testHistoryApi = {
  /**
   * Create a new test history record
   */
  async create(data: TestHistoryCreate): Promise<TestHistoryResponse> {
    const response = await apiClient.request<TestHistoryResponse>('/api/v1/test-history/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data!;
  },

  /**
   * Update an existing test history record
   */
  async update(testId: string, data: TestHistoryUpdate): Promise<TestHistoryResponse> {
    const response = await apiClient.request<TestHistoryResponse>(`/api/v1/test-history/${testId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data!;
  },

  /**
   * Get paginated list of test history
   */
  async list(params: TestHistoryListParams = {}): Promise<TestHistoryListResponse> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, String(value));
      }
    });

    const url = `/api/v1/test-history/${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    const response = await apiClient.request<TestHistoryListResponse>(url);
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data!;
  },

  /**
   * Get detailed test history by test ID
   */
  async getDetail(testId: string): Promise<TestHistoryDetailResponse> {
    const response = await apiClient.request<TestHistoryDetailResponse>(`/api/v1/test-history/${testId}`);
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data!;
  },

  /**
   * Delete a test history record
   */
  async delete(testId: string): Promise<{ message: string }> {
    const response = await apiClient.request<{ message: string }>(`/api/v1/test-history/${testId}`, {
      method: 'DELETE',
    });
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data!;
  },

  /**
   * Get test history for a specific agent
   */
  async getByAgent(agentId: string, params: Omit<TestHistoryListParams, 'agent_id'> = {}): Promise<TestHistoryListResponse> {
    return this.list({ ...params, agent_id: agentId });
  },

  /**
   * Get recent test history (last 10 tests)
   */
  async getRecent(): Promise<TestHistoryResponse[]> {
    const response = await this.list({ limit: 10, page: 1 });
    return response.tests;
  }
};
