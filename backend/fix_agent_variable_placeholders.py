#!/usr/bin/env python3
"""
Fix agent configurations to include proper variable placeholders.

This script updates existing agent configurations to include semantic variable
placeholders in their system prompts, enabling proper variable tracking.
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def fix_agent_variable_placeholders():
    """Fix agent configurations to include proper variable placeholders."""
    print("🔧 Fixing Agent Variable Placeholders...\n")
    
    try:
        from app.core.database import AsyncSessionLocal
        from sqlalchemy import text
        
        async with AsyncSessionLocal() as db:
            # Get all agents
            query = "SELECT id, agent_id, team_name, team_plan FROM agents"
            result = await db.execute(text(query))
            agents = result.fetchall()
            
            print(f"📋 Found {len(agents)} agents to process\n")
            
            updated_count = 0
            
            for agent in agents:
                print(f"🤖 Processing agent: {agent.agent_id} - {agent.team_name}")
                
                if not agent.team_plan:
                    print("  ⚠️ No team_plan found, skipping")
                    continue
                
                # Parse team plan
                team_plan = json.loads(agent.team_plan) if isinstance(agent.team_plan, str) else agent.team_plan
                team_members = team_plan.get("team_members", [])
                
                if not team_members:
                    print("  ⚠️ No team_members found, skipping")
                    continue
                
                # Check if variables already exist
                has_variables = False
                for member in team_members:
                    system_prompt = member.get("system_prompt", "")
                    if "{" in system_prompt and "}" in system_prompt:
                        has_variables = True
                        break
                
                if has_variables:
                    print("  ✅ Variables already present, skipping")
                    continue
                
                # Add semantic variable placeholders based on agent roles
                updated_members = []
                for i, member in enumerate(team_members):
                    role = member.get("role", f"member_{i}")
                    system_prompt = member.get("system_prompt", "")
                    
                    # Create semantic variable based on role
                    if "writer" in role.lower() or "content" in role.lower():
                        variable_name = f"{role}.content"
                        variable_description = "written content and documentation"
                    elif "architect" in role.lower() or "design" in role.lower():
                        variable_name = f"{role}.design"
                        variable_description = "architectural design and structure"
                    elif "analyst" in role.lower():
                        variable_name = f"{role}.analysis"
                        variable_description = "analysis results and insights"
                    elif "planner" in role.lower() or "strategy" in role.lower():
                        variable_name = f"{role}.strategy"
                        variable_description = "strategic plan and approach"
                    elif "executor" in role.lower() or "implement" in role.lower():
                        variable_name = f"{role}.output"
                        variable_description = "execution results and deliverables"
                    elif "reviewer" in role.lower() or "quality" in role.lower():
                        variable_name = f"{role}.review"
                        variable_description = "review feedback and recommendations"
                    else:
                        variable_name = f"{role}.output"
                        variable_description = f"{role} output and results"
                    
                    # Update system prompt to include variable output
                    if system_prompt:
                        # Add variable output instruction
                        enhanced_prompt = f"{system_prompt}\n\nProvide your output as {{{variable_name}}} which will be used by other team members."
                        member["system_prompt"] = enhanced_prompt
                        
                        print(f"    ✅ Enhanced {role} with variable: {{{variable_name}}}")
                    
                    updated_members.append(member)
                
                # Update team plan
                team_plan["team_members"] = updated_members
                
                # Update database
                update_query = """
                UPDATE agents 
                SET team_plan = :team_plan, updated_at = datetime('now')
                WHERE id = :agent_id
                """
                
                await db.execute(text(update_query), {
                    "team_plan": json.dumps(team_plan),
                    "agent_id": agent.id
                })
                
                updated_count += 1
                print(f"  ✅ Updated agent configuration")
            
            # Commit all changes
            await db.commit()
            
            print(f"\n📊 Summary:")
            print(f"  - Total agents processed: {len(agents)}")
            print(f"  - Agents updated: {updated_count}")
            print(f"  - Agents skipped: {len(agents) - updated_count}")
            
            if updated_count > 0:
                print(f"\n🎉 Successfully updated {updated_count} agent configurations!")
                print("   Variables will now be properly tracked during execution.")
            else:
                print(f"\n💡 No agents needed updates - all configurations already have variables.")
            
            return updated_count > 0
        
    except Exception as e:
        print(f"❌ Error fixing agent variable placeholders: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_fixed_agent():
    """Test a fixed agent configuration."""
    print("\n🧪 Testing Fixed Agent Configuration...\n")
    
    try:
        from app.core.database import AsyncSessionLocal
        from sqlalchemy import text
        
        async with AsyncSessionLocal() as db:
            # Get the first agent with variables
            query = """
            SELECT agent_id, team_name, team_plan 
            FROM agents 
            WHERE team_plan LIKE '%{%}%' 
            LIMIT 1
            """
            result = await db.execute(text(query))
            agent = result.fetchone()
            
            if not agent:
                print("❌ No agents with variables found")
                return False
            
            print(f"🤖 Testing agent: {agent.agent_id} - {agent.team_name}")
            
            # Parse team plan
            team_plan = json.loads(agent.team_plan) if isinstance(agent.team_plan, str) else agent.team_plan
            
            # Test variable discovery
            from app.services.variable_discovery import VariableDiscoveryService
            discovery_service = VariableDiscoveryService()
            variables = discovery_service.discover_team_variables(team_plan)
            
            print(f"✅ Variable discovery found {len(variables)} variables:")
            for var in variables:
                if var.source_agent:  # Only show agent-generated variables
                    print(f"  - {var.placeholder} (from {var.source_agent}): {var.semantic_description}")
            
            # Test ConfigDrivenAgent
            from app.services.dynamic_loader import ConfigDrivenAgent
            
            agent_config = {
                "agent_id": agent.agent_id,
                "team_name": agent.team_name,
                "team_plan": team_plan
            }
            
            config_agent = ConfigDrivenAgent(agent_config)
            
            # Test variable discovery for first team member
            if team_plan.get("team_members"):
                first_member_role = team_plan["team_members"][0].get("role", "unknown")
                discovered_vars = await config_agent._get_discovered_variables_for_agent(first_member_role)
                
                print(f"✅ ConfigDrivenAgent found {len(discovered_vars)} variables for {first_member_role}")
                for var in discovered_vars:
                    print(f"  - {var.get('name')}: {var.get('semantic_description')}")
            
            return len(variables) > 0
        
    except Exception as e:
        print(f"❌ Error testing fixed agent: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main function to fix and test agent configurations."""
    print("🚀 Agent Variable Placeholder Fix Tool\n")
    
    # Step 1: Fix agent configurations
    fix_success = await fix_agent_variable_placeholders()
    
    # Step 2: Test a fixed configuration
    if fix_success:
        test_success = await test_fixed_agent()
        
        if test_success:
            print("\n🎉 All tests passed! Agent variable tracking should now work correctly.")
            print("\n💡 Next steps:")
            print("1. Test agent execution in the frontend")
            print("2. Verify that variables appear in the execution and results tabs")
            print("3. Check that variables are saved to the database")
        else:
            print("\n⚠️ Fix applied but testing failed. Check the configuration.")
    else:
        print("\n💡 No fixes were needed or fix failed. Check existing configurations.")

if __name__ == "__main__":
    asyncio.run(main())
