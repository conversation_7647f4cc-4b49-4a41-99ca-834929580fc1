"""add_individual_agent_output_tracking

Revision ID: b36a9a18471d
Revises: 6f6a34829ef8
Create Date: 2025-08-05 09:07:36.817400

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b36a9a18471d'
down_revision = '6f6a34829ef8'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Add new columns for individual agent output tracking
    op.add_column('test_history', sa.Column('individual_agent_outputs', sa.JSON(), nullable=True))
    op.add_column('test_history', sa.Column('agent_execution_sequence', sa.JSON(), nullable=True))
    op.add_column('test_history', sa.Column('inter_agent_variables', sa.JSON(), nullable=True))
    op.add_column('test_history', sa.Column('agent_performance_metrics', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Remove the individual agent output tracking columns
    op.drop_column('test_history', 'agent_performance_metrics')
    op.drop_column('test_history', 'inter_agent_variables')
    op.drop_column('test_history', 'agent_execution_sequence')
    op.drop_column('test_history', 'individual_agent_outputs')
    # ### end Alembic commands ###
