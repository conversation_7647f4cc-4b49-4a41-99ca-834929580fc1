"""
Agent management endpoints.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import get_current_active_user
from app.core.exceptions import AgentCreationError, NotFoundError, ValidationError
from app.core.logging import api_logger
from app.core.timezone_utils import utc_now, normalize_datetime_for_db
from app.services.logging_service import log_agent_operation
from app.models.agent import (
    Agent, AgentStatus, AgentType, AgentCreate,
    AgentResponse, AgentWithTeamResponse, AgentUpdate
)
from app.models.user import User
from app.models.favorites import (
    UserAgentFavorite, UserAgentFavoriteCreate,
    FavoriteAgentResponse, ToggleFavoriteResponse
)
from app.services.dynamic_loader import DynamicLoader
from app.services.variable_discovery import VariableDiscoveryService, VariableMetadata
from app.services.websocket_service import variable_tracker

router = APIRouter()


async def validate_agent_ownership(
    agent_id: str,
    user_id: int,
    db: AsyncSession,
    require_active: bool = False
) -> Optional[Any]:
    """
    Validate that an agent belongs to the specified user.

    Args:
        agent_id: The agent ID to check
        user_id: The user ID that should own the agent
        db: Database session
        require_active: If True, also check that agent status is ACTIVE

    Returns:
        Agent record if found and owned by user, None otherwise

    Raises:
        NotFoundError: If agent not found or not owned by user
    """
    query = "SELECT * FROM agents WHERE agent_id = :agent_id AND user_id = :user_id"
    params = {"agent_id": agent_id, "user_id": user_id}

    if require_active:
        query += " AND status = :status"
        params["status"] = AgentStatus.ACTIVE

    result = await db.execute(text(query), params)
    agent = result.fetchone()

    if not agent:
        raise NotFoundError("Agent", agent_id)

    return agent


@router.post("/from-template", response_model=Dict[str, Any])
async def create_agent_from_template(
    request: Dict[str, Any],
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Create agent directly from a complete template (no AI generation needed)."""
    try:
        template_id = request.get("template_id")
        customizations = request.get("customizations", {})

        if not template_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="template_id is required"
            )

        # Get complete agent configuration from template
        from app.services.template_management_service import TemplateManagementService
        template_service = TemplateManagementService(db)

        agent_config = await template_service.transform_template_for_agent_creation(
            template_id, current_user, customizations
        )

        # Generate agent ID
        agent_id = f"agent_{uuid.uuid4().hex[:12]}"

        # Extract team members for database storage
        team_members = agent_config.get("team_members", [])

        # Determine initial status based on template completeness
        initial_status = AgentStatus.ACTIVE if agent_config["metadata"]["ready_to_deploy"] else AgentStatus.CREATING

        # Create agent record
        agent = Agent(
            agent_id=agent_id,
            team_name=agent_config.get("team_name", "Unnamed Team"),
            description=agent_config.get("description", ""),
            team_plan=agent_config["team_plan"],
            team_members=team_members,
            prompt_template=agent_config.get("prompt_template", ""),
            system_prompt=agent_config.get("system_prompt", ""),
            user_id=current_user.id,
            status=initial_status,
            agent_type=AgentType.TEAM,
            usage_count=0,
        )

        db.add(agent)
        await db.commit()
        await db.refresh(agent)

        # Generate API endpoint URL
        api_endpoint = f"http://localhost:8000/api/v1/agents/{agent_id}/execute"

        response = {
            "agent_id": agent_id,
            "status": initial_status.value,
            "api_endpoint": api_endpoint,
            "ready_to_deploy": agent_config["metadata"]["ready_to_deploy"],
            "created_from_template": True,
            "template_id": template_id,
        }

        if agent_config["metadata"]["ready_to_deploy"]:
            # Agent is ready to use immediately
            response.update({
                "message": "Agent created successfully from template and is ready to use",
                "estimated_time": "0 seconds",
            })

            api_logger.info(
                "Agent created directly from complete template",
                agent_id=agent_id,
                template_id=template_id,
                team_name=agent.team_name,
                team_members_count=len(team_members),
            )
        else:
            # Agent needs AI generation (fallback for incomplete templates)
            background_tasks.add_task(
                process_agent_creation,
                agent.id,
                agent_config["team_plan"],
            )

            response.update({
                "message": "Agent creation started (template requires AI enhancement)",
                "estimated_time": "60-120 seconds",
            })

            api_logger.info(
                "Agent creation started from incomplete template",
                agent_id=agent_id,
                template_id=template_id,
                team_name=agent.team_name,
            )

        return response

    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        api_logger.error(f"Failed to create agent from template: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create agent from template: {str(e)}",
        )


@router.post("/create", response_model=Dict[str, Any])
async def create_agent_from_plan(
    team_plan: Dict[str, Any],
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Create agent from team plan."""
    try:
        # Generate agent ID
        agent_id = f"agent_{uuid.uuid4().hex[:12]}"
        
        # Extract team members from team_plan
        team_members = []
        if "team_members" in team_plan:
            team_members = [
                {
                    "name": member.get("name", "Unknown"),
                    "role": member.get("role", "Member"),
                    "description": member.get("description", ""),
                    "system_prompt": member.get("system_prompt", "")
                }
                for member in team_plan["team_members"]
            ]
        elif "specialists" in team_plan:
            # Handle legacy format
            team_members = [
                {
                    "name": specialist.get("name", "Unknown"),
                    "role": specialist.get("role", "Member"),
                    "description": specialist.get("description", ""),
                    "system_prompt": specialist.get("system_prompt", "")
                }
                for specialist in team_plan["specialists"]
            ]



        # Create agent record with complete team information
        agent = Agent(
            agent_id=agent_id,
            team_name=team_plan.get("team_name", "Unnamed Team"),
            description=team_plan.get("description", ""),
            team_plan=team_plan,  # Store complete team plan
            team_members=team_members,  # Store extracted team members
            prompt_template=team_plan.get("prompt_template", ""),
            system_prompt=team_plan.get("system_prompt", ""),
            user_id=current_user.id,  # Associate with current user
            status=AgentStatus.CREATING,
            agent_type=AgentType.TEAM,
            usage_count=0,
        )

        db.add(agent)
        await db.commit()
        await db.refresh(agent)
        
        # Start agent creation process in background
        background_tasks.add_task(
            process_agent_creation,
            agent.id,
            team_plan,
        )
        
        api_logger.info(
            "Agent creation started",
            agent_id=agent_id,
            team_name=agent.team_name,
        )

        # Log agent creation event
        await log_agent_operation(
            event_type="agent_create",
            agent_id=agent_id,
            user_id=current_user.id,
            message=f"Agent creation started: {agent.team_name}",
            metadata={
                "team_name": agent.team_name,
                "description": agent.description,
                "team_members_count": len(team_members),
                "agent_type": agent.agent_type.value
            }
        )
        
        # Generate API endpoint URL (full URL for frontend display)
        api_endpoint = f"http://localhost:8000/api/v1/agents/{agent_id}/execute"

        return {
            "agent_id": agent_id,
            "status": "creating",
            "message": "Agent creation started",
            "api_endpoint": api_endpoint,
            "estimated_time": "60-120 seconds",
        }
        
    except Exception as e:
        api_logger.error(f"Failed to create agent: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create agent: {str(e)}",
        )


@router.get("/", response_model=List[AgentWithTeamResponse])
async def list_agents(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status_filter: Optional[AgentStatus] = Query(None),
    search: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> List[AgentWithTeamResponse]:
    """List agents with filtering and pagination."""
    try:
        # Build query - exclude deleted agents by default and filter by user
        query = "SELECT * FROM agents WHERE status != :deleted_status AND user_id = :user_id"
        params = {"deleted_status": AgentStatus.DELETED, "user_id": current_user.id}

        if status_filter:
            query += " AND status = :status_filter"
            params["status_filter"] = status_filter

        if search:
            query += " AND (team_name LIKE :search OR description LIKE :search)"
            params["search"] = f"%{search}%"

        query += " ORDER BY created_at DESC LIMIT :limit OFFSET :skip"
        params.update({"limit": limit, "skip": skip})

        result = await db.execute(text(query), params)
        agents = result.fetchall()

        # Initialize dynamic loader
        loader = DynamicLoader()

        # Convert Row objects to dict and enhance with team info
        agent_list = []
        for agent in agents:
            agent_dict = {column: getattr(agent, column) for column in agent._fields}

            # Fix enum case mismatches - convert uppercase to lowercase
            if 'agent_type' in agent_dict and agent_dict['agent_type']:
                agent_dict['agent_type'] = agent_dict['agent_type'].lower()

            if 'status' in agent_dict and agent_dict['status']:
                agent_dict['status'] = agent_dict['status'].lower()

            # Parse JSON fields if they are strings
            if 'team_plan' in agent_dict and isinstance(agent_dict['team_plan'], str):
                try:
                    import json
                    agent_dict['team_plan'] = json.loads(agent_dict['team_plan'])
                except (json.JSONDecodeError, TypeError):
                    agent_dict['team_plan'] = None

            if 'team_members' in agent_dict and isinstance(agent_dict['team_members'], str):
                try:
                    import json
                    agent_dict['team_members'] = json.loads(agent_dict['team_members'])
                except (json.JSONDecodeError, TypeError):
                    agent_dict['team_members'] = []

            # Ensure required fields have default values
            agent_dict.setdefault('last_used', None)
            agent_dict.setdefault('avg_response_time', None)
            agent_dict.setdefault('success_rate', None)
            agent_dict.setdefault('updated_at', agent_dict.get('created_at'))

            # team_members field now supports Dict[str, Any] so no conversion needed

            # Use team_plan from database if available, otherwise try to get from config file
            if agent_dict.get("team_plan"):
                # Team plan is stored in database - use it directly
                team_members = agent_dict.get("team_members", [])

                # Set specialists for backward compatibility
                agent_dict["specialists"] = team_members or []
            else:
                # Fallback: try to get team information from config file
                agent_info = loader.get_agent_info(agent.agent_id)
                if agent_info and agent_info.get("team_plan"):
                    team_plan = agent_info["team_plan"]
                    agent_dict["team_plan"] = team_plan

                    # Extract specialists from team_members
                    team_members = team_plan.get("team_members", [])
                    if team_members:
                        agent_dict["specialists"] = [
                            {"name": member.get("name", "Unknown"), "role": member.get("role", "Member")}
                            for member in team_members
                        ]
                        agent_dict["team_members"] = [
                            {
                                "name": member.get("name", "Unknown"),
                                "role": member.get("role", "Member"),
                                "description": member.get("description", ""),
                                "system_prompt": member.get("system_prompt", "")
                            }
                            for member in team_members
                        ]
                    else:
                        # Fallback to specialists field if available
                        specialists = team_plan.get("specialists", [])
                        # specialists field now supports Dict[str, Any] so no conversion needed
                        agent_dict["specialists"] = specialists
                        agent_dict["team_members"] = specialists
                else:
                    # Provide fallback team member data when no config file exists
                    agent_dict["team_plan"] = {
                        "team_name": agent_dict.get("team_name", "Unknown Team"),
                        "description": agent_dict.get("description", "No description available"),
                        "team_members": []
                    }

                    # Generate mock specialists based on team name for demo purposes
                    team_name = agent_dict.get("team_name", "").lower()
                    if "support" in team_name:
                        mock_members = [
                            {"name": "Alex", "role": "客服专员", "description": "专业客服", "system_prompt": "你是一个专业的客服专员"},
                            {"name": "Sarah", "role": "技术支持", "description": "技术专家", "system_prompt": "你是一个技术支持专家"}
                        ]
                    elif "marketing" in team_name:
                        mock_members = [
                            {"name": "Mike", "role": "营销策略师", "description": "营销专家", "system_prompt": "你是一个营销策略专家"},
                            {"name": "Lisa", "role": "内容创作者", "description": "内容专家", "system_prompt": "你是一个内容创作专家"}
                        ]
                    elif "test" in team_name:
                        mock_members = [
                            {"name": "John", "role": "测试工程师", "description": "测试专家", "system_prompt": "你是一个测试工程师"},
                            {"name": "Emma", "role": "质量保证", "description": "质量专家", "system_prompt": "你是一个质量保证专家"}
                        ]
                    else:
                        # Default specialists for unknown teams
                        mock_members = [
                            {"name": "Agent A", "role": "专家", "description": "通用专家", "system_prompt": "你是一个专家"},
                            {"name": "Agent B", "role": "助手", "description": "通用助手", "system_prompt": "你是一个助手"}
                        ]

                    agent_dict["specialists"] = [{"name": m["name"], "role": m["role"]} for m in mock_members]
                    agent_dict["team_members"] = mock_members

            try:
                agent_response = AgentWithTeamResponse(**agent_dict)
                agent_list.append(agent_response)
            except Exception as e:
                api_logger.error(f"Failed to create AgentWithTeamResponse for agent {agent_dict.get('agent_id', 'unknown')}: {str(e)}")
                api_logger.error(f"Agent dict keys: {list(agent_dict.keys())}")
                # Skip this agent if we can't create a valid response
                continue
        return agent_list
        
    except Exception as e:
        api_logger.error(f"Failed to list agents: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list agents: {str(e)}",
        )


# Agent Favorites Endpoints (must be before /{agent_id} route)

@router.get("/favorites", response_model=List[FavoriteAgentResponse])
async def get_favorite_agents(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    include_performance: bool = Query(True, description="Include performance metrics")
) -> List[FavoriteAgentResponse]:
    """Get user's favorite agents with optional performance metrics."""
    try:
        # Query favorite agents with agent details
        favorites_query = """
            SELECT
                f.id as favorite_id,
                f.uuid as favorite_uuid,
                f.created_at as favorited_at,
                a.agent_id,
                a.team_name as name,
                a.description,
                a.status,
                a.agent_type,
                a.created_at,
                a.updated_at,
                a.last_used,
                a.usage_count
            FROM user_agent_favorites f
            JOIN agents a ON f.agent_id = a.agent_id
            WHERE f.user_id = :user_id AND a.user_id = :user_id
            ORDER BY f.created_at DESC
        """

        result = await db.execute(
            text(favorites_query),
            {"user_id": current_user.id}
        )
        favorites = result.fetchall()

        favorite_agents = []
        for favorite in favorites:
            # Base agent data
            agent_data = {
                "agent_id": favorite.agent_id,
                "name": favorite.name,
                "description": favorite.description,
                "status": favorite.status,
                "agent_type": favorite.agent_type,
                "created_at": favorite.created_at,
                "updated_at": favorite.updated_at,
                "last_used": favorite.last_used,
                "usage_count": favorite.usage_count,
                "favorite_id": favorite.favorite_id,
                "favorite_uuid": favorite.favorite_uuid,
                "favorited_at": favorite.favorited_at,
                "performance": None
            }

            # Add performance metrics if requested
            if include_performance:
                try:
                    performance_query = """
                        SELECT
                            execution_count,
                            success_count,
                            error_count,
                            avg_response_time,
                            total_cost,
                            last_execution_time
                        FROM agent_metrics
                        WHERE agent_id = :agent_id AND user_id = :user_id
                    """
                    perf_result = await db.execute(
                        text(performance_query),
                        {"agent_id": favorite.agent_id, "user_id": current_user.id}
                    )
                    perf_data = perf_result.fetchone()

                    if perf_data:
                        success_rate = (
                            (perf_data.success_count / perf_data.execution_count * 100)
                            if perf_data.execution_count > 0 else 0
                        )
                        agent_data["performance"] = {
                            "execution_count": perf_data.execution_count,
                            "success_count": perf_data.success_count,
                            "error_count": perf_data.error_count,
                            "success_rate": round(success_rate, 1),
                            "avg_response_time": perf_data.avg_response_time,
                            "total_cost": perf_data.total_cost,
                            "last_execution_time": perf_data.last_execution_time
                        }
                except Exception as perf_error:
                    api_logger.warning(
                        "Failed to fetch performance metrics for favorite agent",
                        agent_id=favorite.agent_id,
                        error=str(perf_error)
                    )
                    # Continue without performance data

            favorite_agents.append(FavoriteAgentResponse(**agent_data))

        api_logger.info(
            "Retrieved favorite agents",
            user_id=current_user.id,
            count=len(favorite_agents)
        )

        return favorite_agents

    except Exception as e:
        api_logger.error(
            "Failed to retrieve favorite agents",
            user_id=current_user.id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve favorite agents: {str(e)}"
        )


@router.post("/{agent_id}/favorite", response_model=ToggleFavoriteResponse)
async def toggle_agent_favorite(
    agent_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> ToggleFavoriteResponse:
    """Toggle favorite status for an agent."""
    try:
        # Validate agent exists and user has access
        agent = await validate_agent_ownership(agent_id, current_user.id, db)
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent {agent_id} not found or access denied"
            )

        # Check if already favorited
        favorite_query = """
            SELECT * FROM user_agent_favorites
            WHERE user_id = :user_id AND agent_id = :agent_id
        """
        result = await db.execute(
            text(favorite_query),
            {"user_id": current_user.id, "agent_id": agent_id}
        )
        existing_favorite = result.fetchone()

        if existing_favorite:
            # Remove favorite
            delete_query = """
                DELETE FROM user_agent_favorites
                WHERE user_id = :user_id AND agent_id = :agent_id
            """
            await db.execute(
                text(delete_query),
                {"user_id": current_user.id, "agent_id": agent_id}
            )
            await db.commit()

            return ToggleFavoriteResponse(
                success=True,
                is_favorite=False,
                message="Agent removed from favorites",
                agent_id=agent_id,
                favorite_id=None
            )
        else:
            # Add favorite
            favorite_uuid = uuid.uuid4().hex
            insert_query = """
                INSERT INTO user_agent_favorites (uuid, user_id, agent_id, created_at, updated_at)
                VALUES (:uuid, :user_id, :agent_id, :created_at, :updated_at)
            """
            await db.execute(
                text(insert_query),
                {
                    "uuid": favorite_uuid,
                    "user_id": current_user.id,
                    "agent_id": agent_id,
                    "created_at": normalize_datetime_for_db(utc_now()),
                    "updated_at": None
                }
            )
            await db.commit()

            # Get the created favorite ID
            id_query = """
                SELECT id FROM user_agent_favorites
                WHERE user_id = :user_id AND agent_id = :agent_id
            """
            result = await db.execute(
                text(id_query),
                {"user_id": current_user.id, "agent_id": agent_id}
            )
            favorite_record = result.fetchone()
            favorite_id = favorite_record[0] if favorite_record else None

            return ToggleFavoriteResponse(
                success=True,
                is_favorite=True,
                message="Agent added to favorites",
                agent_id=agent_id,
                favorite_id=favorite_id
            )

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(
            "Failed to toggle agent favorite",
            agent_id=agent_id,
            user_id=current_user.id,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to toggle favorite status: {str(e)}"
        )


@router.get("/{agent_id}", response_model=AgentResponse)
async def get_agent(
    agent_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> AgentResponse:
    """Get agent by ID."""
    try:
        result = await db.execute(
            text("SELECT * FROM agents WHERE agent_id = :agent_id AND user_id = :user_id"),
            {"agent_id": agent_id, "user_id": current_user.id}
        )
        agent = result.fetchone()

        if not agent:
            raise NotFoundError("Agent", agent_id)
        
        # Convert Row to dict
        agent_dict = {column: getattr(agent, column) for column in agent._fields}

        # Fix enum case mismatches - convert uppercase to lowercase
        if 'agent_type' in agent_dict and agent_dict['agent_type']:
            agent_dict['agent_type'] = agent_dict['agent_type'].lower()

        if 'status' in agent_dict and agent_dict['status']:
            agent_dict['status'] = agent_dict['status'].lower()

        # Parse JSON fields if they are strings (same as in list_agents and update_agent)
        if 'team_plan' in agent_dict and isinstance(agent_dict['team_plan'], str):
            try:
                import json
                agent_dict['team_plan'] = json.loads(agent_dict['team_plan'])
            except (json.JSONDecodeError, TypeError):
                agent_dict['team_plan'] = None

        if 'team_members' in agent_dict and isinstance(agent_dict['team_members'], str):
            try:
                import json
                agent_dict['team_members'] = json.loads(agent_dict['team_members'])
            except (json.JSONDecodeError, TypeError):
                agent_dict['team_members'] = []

        # Ensure required fields have default values
        agent_dict.setdefault('last_used', None)
        agent_dict.setdefault('avg_response_time', None)
        agent_dict.setdefault('success_rate', None)

        return AgentResponse(**agent_dict)
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Agent '{agent_id}' not found",
        )
    except Exception as e:
        api_logger.error(f"Failed to get agent: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get agent: {str(e)}",
        )


@router.put("/{agent_id}", response_model=AgentResponse)
@router.patch("/{agent_id}", response_model=AgentResponse)
async def update_agent(
    agent_id: str,
    agent_update: AgentUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> AgentResponse:
    """Update agent."""
    try:
        # Check if agent exists and belongs to current user
        result = await db.execute(
            text("SELECT * FROM agents WHERE agent_id = :agent_id AND user_id = :user_id"),
            {"agent_id": agent_id, "user_id": current_user.id}
        )
        agent = result.fetchone()

        if not agent:
            raise NotFoundError("Agent", agent_id)
        
        # Update agent
        update_data = agent_update.model_dump(exclude_unset=True)
        if update_data:
            update_data["updated_at"] = normalize_datetime_for_db(utc_now())

            # Serialize JSON fields for database storage
            if "team_plan" in update_data and update_data["team_plan"] is not None:
                import json
                update_data["team_plan"] = json.dumps(update_data["team_plan"])

            if "team_members" in update_data and update_data["team_members"] is not None:
                import json
                update_data["team_members"] = json.dumps(update_data["team_members"])

            # Build update query with named parameters
            set_clause = ", ".join([f"{key} = :{key}" for key in update_data.keys()])
            query = f"UPDATE agents SET {set_clause} WHERE agent_id = :agent_id"
            update_data["agent_id"] = agent_id

            await db.execute(text(query), update_data)
            await db.commit()
        
        # Get updated agent
        result = await db.execute(
            text("SELECT * FROM agents WHERE agent_id = :agent_id"), {"agent_id": agent_id}
        )
        updated_agent = result.fetchone()

        if not updated_agent:
            raise NotFoundError("Agent", agent_id)

        api_logger.info("Agent updated", agent_id=agent_id)
        # Convert Row to dict
        agent_dict = {column: getattr(updated_agent, column) for column in updated_agent._fields}

        # Fix enum case mismatches - convert uppercase to lowercase
        if 'agent_type' in agent_dict and agent_dict['agent_type']:
            agent_dict['agent_type'] = agent_dict['agent_type'].lower()

        if 'status' in agent_dict and agent_dict['status']:
            agent_dict['status'] = agent_dict['status'].lower()

        # Parse JSON fields if they are strings (same as in list_agents)
        if 'team_plan' in agent_dict and isinstance(agent_dict['team_plan'], str):
            try:
                import json
                agent_dict['team_plan'] = json.loads(agent_dict['team_plan'])
            except (json.JSONDecodeError, TypeError):
                agent_dict['team_plan'] = None

        if 'team_members' in agent_dict and isinstance(agent_dict['team_members'], str):
            try:
                import json
                agent_dict['team_members'] = json.loads(agent_dict['team_members'])
            except (json.JSONDecodeError, TypeError):
                agent_dict['team_members'] = []

        # Ensure required fields have default values
        agent_dict.setdefault('last_used', None)
        agent_dict.setdefault('avg_response_time', None)
        agent_dict.setdefault('success_rate', None)
        agent_dict.setdefault('updated_at', agent_dict.get('created_at'))

        # Fix team_members field types for Pydantic validation
        if 'team_members' in agent_dict and agent_dict['team_members']:
            fixed_team_members = []
            for member in agent_dict['team_members']:
                if isinstance(member, dict):
                    fixed_member = member.copy()
                    # Convert list/dict fields to strings for Pydantic validation
                    for field in ['capabilities', 'tools', 'model_config']:
                        if field in fixed_member and not isinstance(fixed_member[field], str):
                            if isinstance(fixed_member[field], (list, dict)):
                                import json
                                fixed_member[field] = json.dumps(fixed_member[field])
                            else:
                                fixed_member[field] = str(fixed_member[field])
                    fixed_team_members.append(fixed_member)
                else:
                    fixed_team_members.append(member)
            agent_dict['team_members'] = fixed_team_members

        return AgentResponse(**agent_dict)
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Agent '{agent_id}' not found",
        )
    except Exception as e:
        api_logger.error(f"Failed to update agent: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update agent: {str(e)}",
        )


@router.delete("/{agent_id}")
async def delete_agent(
    agent_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Delete agent."""
    try:
        # Check if agent exists and belongs to current user
        result = await db.execute(
            text("SELECT * FROM agents WHERE agent_id = :agent_id AND user_id = :user_id"),
            {"agent_id": agent_id, "user_id": current_user.id}
        )
        agent = result.fetchone()

        if not agent:
            raise NotFoundError("Agent", agent_id)

        # Soft delete by updating status
        await db.execute(
            text("UPDATE agents SET status = :status, updated_at = :updated_at WHERE agent_id = :agent_id AND user_id = :user_id"),
            {"status": AgentStatus.DELETED, "updated_at": normalize_datetime_for_db(utc_now()), "agent_id": agent_id, "user_id": current_user.id},
        )
        await db.commit()
        
        api_logger.info("Agent deleted", agent_id=agent_id)
        return {"message": f"Agent '{agent_id}' deleted successfully"}
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Agent '{agent_id}' not found",
        )
    except Exception as e:
        api_logger.error(f"Failed to delete agent: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete agent: {str(e)}",
        )


@router.post("/{agent_id}/execute", response_model=Dict[str, Any])
async def execute_agent(
    agent_id: str,
    input_data: Dict[str, Any],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Execute agent with input data and return response."""
    try:
        # Check if agent exists, is active, and belongs to current user
        result = await db.execute(
            text("SELECT * FROM agents WHERE agent_id = :agent_id AND status = :status AND user_id = :user_id"),
            {"agent_id": agent_id, "status": AgentStatus.ACTIVE, "user_id": current_user.id},
        )
        agent = result.fetchone()

        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Active agent '{agent_id}' not found",
            )

        # Create execution record
        execution_id = f"exec_{uuid.uuid4().hex[:12]}"

        # Extract agent configuration for config-driven execution
        agent_dict = {column: getattr(agent, column) for column in agent._fields}

        # Parse JSON fields if they are strings
        if 'team_plan' in agent_dict and isinstance(agent_dict['team_plan'], str):
            try:
                import json
                agent_dict['team_plan'] = json.loads(agent_dict['team_plan'])
            except (json.JSONDecodeError, TypeError):
                agent_dict['team_plan'] = {}

        if 'team_members' in agent_dict and isinstance(agent_dict['team_members'], str):
            try:
                import json
                agent_dict['team_members'] = json.loads(agent_dict['team_members'])
            except (json.JSONDecodeError, TypeError):
                agent_dict['team_members'] = []

        # Extract test_id from input_data if provided
        test_id = input_data.get("test_id")

        # Execute agent synchronously and get response
        response = await execute_agent_sync(agent_id, input_data, agent_dict, current_user, test_id=test_id)

        # Note: Variable data is updated in database during step completion in _track_step_variables

        # Update agent usage statistics
        await db.execute(
            text("UPDATE agents SET last_used = :last_used, usage_count = usage_count + 1 WHERE agent_id = :agent_id"),
            {"last_used": normalize_datetime_for_db(utc_now()), "agent_id": agent_id},
        )
        await db.commit()

        api_logger.info(
            "Agent execution completed",
            agent_id=agent_id,
            execution_id=execution_id,
        )

        return {
            "success": True,
            "data": {
                "execution_id": execution_id,
                "response": response,
                "agent_id": agent_id,
                "timestamp": utc_now().isoformat()
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to execute agent: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to execute agent: {str(e)}",
        )


@router.post("/{agent_id}/execute/stream")
async def execute_agent_stream(
    agent_id: str,
    input_data: Dict[str, Any],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """Execute agent with streaming response."""
    from fastapi.responses import StreamingResponse
    import json
    import asyncio

    async def generate_stream():
        try:
            # Check if agent exists, is active, and belongs to current user
            result = await db.execute(
                text("SELECT * FROM agents WHERE agent_id = :agent_id AND status = :status AND user_id = :user_id"),
                {"agent_id": agent_id, "status": AgentStatus.ACTIVE, "user_id": current_user.id},
            )
            agent = result.fetchone()

            if not agent:
                yield f"data: {json.dumps({'type': 'error', 'content': f'Active agent {agent_id} not found'})}\n\n"
                return

            # Convert agent row to dict
            agent_dict = dict(agent._mapping) if hasattr(agent, '_mapping') else dict(agent)

            # Parse JSON fields if they are strings (same as regular execution)
            if 'team_plan' in agent_dict and isinstance(agent_dict['team_plan'], str):
                try:
                    import json
                    agent_dict['team_plan'] = json.loads(agent_dict['team_plan'])
                except (json.JSONDecodeError, TypeError):
                    agent_dict['team_plan'] = {}

            if 'team_members' in agent_dict and isinstance(agent_dict['team_members'], str):
                try:
                    import json
                    agent_dict['team_members'] = json.loads(agent_dict['team_members'])
                except (json.JSONDecodeError, TypeError):
                    agent_dict['team_members'] = []

            # Create progress callback for streaming updates
            async def progress_callback(progress_data):
                yield f"data: {json.dumps({'type': 'progress', **progress_data})}\n\n"

            # Execute agent with progress streaming
            from app.services.dynamic_loader import DynamicLoader
            loader = DynamicLoader()

            # Create a queue to collect progress updates
            progress_queue = asyncio.Queue()

            async def queue_progress_callback(progress_data):
                # Handle streaming content specially
                if progress_data.get("stage") == "streaming":
                    # Send streaming content immediately
                    streaming_content = progress_data.get("streaming_content", "")
                    if streaming_content:
                        await progress_queue.put({
                            "type": "streaming",
                            "content": streaming_content,
                            "partial_output": progress_data.get("partial_output", ""),
                            "step_name": progress_data.get("step_name", ""),
                            "assignee": progress_data.get("assignee", "")
                        })
                else:
                    # Regular progress update
                    await progress_queue.put(progress_data)

                # Extract and broadcast variable updates via WebSocket
                await extract_and_broadcast_variables(agent_id, progress_data, test_id, db)

            # Extract test_id from input_data if provided
            test_id = input_data.get("test_id")

            # Execute agent with progress callback
            async def execute_with_progress():
                try:
                    # Use the loader's execute_agent_stream method for true streaming
                    result = await loader.execute_agent_stream(
                        agent_id,
                        input_data,
                        agent_config=agent_dict,
                        progress_callback=queue_progress_callback,
                        current_user=current_user,  # Pass current_user explicitly
                        test_id=test_id  # Pass test_id for variable tracking
                    )

                    await progress_queue.put({"type": "result", "data": result})
                except Exception as e:
                    await progress_queue.put({"type": "error", "error": str(e)})
                finally:
                    await progress_queue.put({"type": "done"})

            # Start execution in background
            execution_task = asyncio.create_task(execute_with_progress())

            # Stream progress updates
            while True:
                try:
                    # Wait for progress update with timeout
                    progress_data = await asyncio.wait_for(progress_queue.get(), timeout=1.0)

                    if progress_data.get("type") == "done":
                        break
                    elif progress_data.get("type") == "error":
                        yield f"data: {json.dumps({'type': 'error', 'content': progress_data.get('error')})}\n\n"
                        break
                    elif progress_data.get("type") == "streaming":
                        # Send streaming content with proper encoding
                        streaming_data = {
                            'type': 'streaming',
                            'content': progress_data.get('content', ''),
                            'partial_output': progress_data.get('partial_output', ''),
                            'step_name': progress_data.get('step_name', ''),
                            'assignee': progress_data.get('assignee', '')
                        }
                        yield f"data: {json.dumps(streaming_data, ensure_ascii=False)}\n\n"
                    elif progress_data.get("type") == "result":
                        # Send final result
                        result_data = progress_data.get("data", {})
                        final_output = result_data.get("final_output", "执行完成")
                        yield f"data: {json.dumps({'type': 'final_result', 'content': final_output, 'full_result': result_data})}\n\n"
                    else:
                        # Send progress update
                        yield f"data: {json.dumps({'type': 'progress', **progress_data}, ensure_ascii=False)}\n\n"

                except asyncio.TimeoutError:
                    # Send heartbeat to keep connection alive
                    yield f"data: {json.dumps({'type': 'heartbeat'})}\n\n"
                    continue

            # Wait for execution to complete
            await execution_task

            # Send completion signal
            yield f"data: {json.dumps({'type': 'end', 'content': 'Stream completed'})}\n\n"

        except Exception as e:
            yield f"data: {json.dumps({'type': 'error', 'content': str(e)})}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )


@router.get("/{agent_id}/status", response_model=Dict[str, Any])
async def get_agent_status(
    agent_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Get agent status and metrics."""
    try:
        result = await db.execute(
            text("SELECT * FROM agents WHERE agent_id = :agent_id AND user_id = :user_id"),
            {"agent_id": agent_id, "user_id": current_user.id}
        )
        agent = result.fetchone()

        if not agent:
            raise NotFoundError("Agent", agent_id)
        
        # TODO: Get execution statistics
        
        return {
            "agent_id": agent_id,
            "status": agent.status,
            "usage_count": agent.usage_count,
            "last_used": agent.last_used.isoformat() if agent.last_used else None,
            "avg_response_time": agent.avg_response_time,
            "success_rate": agent.success_rate,
            "created_at": agent.created_at.isoformat(),
            "updated_at": agent.updated_at.isoformat() if agent.updated_at else None,
        }
        
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Agent '{agent_id}' not found",
        )
    except Exception as e:
        api_logger.error(f"Failed to get agent status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get agent status: {str(e)}",
        )


@router.get("/{agent_id}/team/variables", response_model=Dict[str, Any])
async def get_agent_team_variables(
    agent_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """
    Discover and return all variable placeholders in the agent's team configuration.

    This endpoint analyzes the agent's team plan and extracts all variable placeholders
    used in agent prompts, categorizes them, and provides metadata about their usage.
    """
    try:
        # Get agent with team configuration
        result = await db.execute(
            text("SELECT * FROM agents WHERE agent_id = :agent_id AND user_id = :user_id"),
            {"agent_id": agent_id, "user_id": current_user.id}
        )
        agent = result.fetchone()

        if not agent:
            raise NotFoundError("Agent", agent_id)

        # Convert Row to dict
        agent_dict = {column: getattr(agent, column) for column in agent._fields}

        # Parse JSON fields if they are strings (same as other endpoints)
        if 'team_plan' in agent_dict and isinstance(agent_dict['team_plan'], str):
            try:
                import json
                agent_dict['team_plan'] = json.loads(agent_dict['team_plan'])
            except (json.JSONDecodeError, TypeError):
                agent_dict['team_plan'] = None

        # Check if agent has team configuration
        team_plan = agent_dict.get("team_plan")
        if not team_plan:
            return {
                "agent_id": agent_id,
                "variables": [],
                "summary": {
                    "total_variables": 0,
                    "user_input_variables": 0,
                    "inter_agent_variables": 0,
                    "system_variables": 0,
                    "output_variables": 0
                },
                "message": "No team configuration found for this agent"
            }

        # Initialize variable discovery service
        discovery_service = VariableDiscoveryService()

        # Discover variables in the team configuration
        variables = discovery_service.discover_team_variables(team_plan)

        # Convert VariableMetadata objects to dictionaries
        variables_data = []
        for var in variables:
            variables_data.append({
                "name": var.name,
                "placeholder": var.placeholder,
                "variable_type": var.variable_type.value,
                "source_agent": var.source_agent,
                "destination_agents": var.destination_agents,
                "semantic_description": var.semantic_description,
                "workflow_step": var.workflow_step,
                "dependencies": var.dependencies,
                "is_required": var.is_required,
                "example_value": var.example_value,
                "status": "pending"  # Initial status for frontend
            })

        # Generate summary statistics
        summary = {
            "total_variables": len(variables_data),
            "user_input_variables": len([v for v in variables_data if v["variable_type"] == "user-input"]),
            "inter_agent_variables": len([v for v in variables_data if v["variable_type"] == "inter-agent"]),
            "system_variables": len([v for v in variables_data if v["variable_type"] == "system"]),
            "output_variables": len([v for v in variables_data if v["variable_type"] == "output"]),
            "context_variables": len([v for v in variables_data if v["variable_type"] == "context"])
        }

        api_logger.info(
            f"Discovered {len(variables_data)} variables for agent {agent_id}",
            extra={
                "agent_id": agent_id,
                "user_id": current_user.id,
                "variable_count": len(variables_data)
            }
        )

        return {
            "agent_id": agent_id,
            "team_name": agent_dict.get("team_name", "Unknown Team"),
            "variables": variables_data,
            "summary": summary,
            "discovery_timestamp": utc_now().isoformat(),
            "message": f"Successfully discovered {len(variables_data)} variables"
        }

    except NotFoundError:
        raise
    except Exception as e:
        api_logger.error(f"Error discovering agent variables: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to discover agent variables",
        )


async def execute_agent_sync(agent_id: str, input_data: Dict[str, Any], agent_config: Optional[Dict] = None, current_user: Optional[User] = None, progress_callback=None, test_id: Optional[str] = None) -> Dict[str, Any]:
    """Execute agent synchronously and return response - supports both config-driven and code-generated agents."""
    try:
        # Initialize dynamic loader
        loader = DynamicLoader()

        # Load agent with configuration support
        agent_instance = await loader.load_agent(agent_id, agent_config)
        if not agent_instance:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent implementation not found for {agent_id}"
            )

        # Set user context for AI service calls
        if current_user and hasattr(agent_instance, 'current_user'):
            agent_instance.current_user = current_user
            api_logger.info(f"User context set for agent {agent_id}: user_id={current_user.id}")
        else:
            api_logger.warning(f"User context not set for agent {agent_id}: current_user={current_user}, has_attr={hasattr(agent_instance, 'current_user') if agent_instance else False}")

        # Set test_id for variable tracking
        if test_id and hasattr(agent_instance, 'set_test_id'):
            agent_instance.set_test_id(test_id)
            api_logger.info(f"Test ID set for sync agent {agent_id}: {test_id}")

        # Prepare input for agent execution
        execution_input = {
            "input": input_data.get("input", ""),
            "user_data": input_data.get("user_data", {}),
            "context": input_data.get("context", {}),
            "agent_id": agent_id,
            "ai_override": input_data.get("ai_override")  # Pass through AI model overrides
        }

        # Execute agent using the new execute method
        if hasattr(agent_instance, 'execute'):
            # New config-driven agent or updated code-generated agent
            if hasattr(agent_instance, 'team_name') and progress_callback:  # ConfigDrivenAgent with progress support
                response = await agent_instance.execute(execution_input, progress_callback)
            else:
                response = await agent_instance.execute(execution_input)
        elif hasattr(agent_instance, 'process_request'):
            # Legacy code-generated agent
            user_input = input_data.get("input", "")
            if not user_input:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Input is required"
                )
            response = await agent_instance.process_request(user_input)

            # Wrap legacy response in new format
            if isinstance(response, str):
                response = {
                    "agent_id": agent_id,
                    "status": "success",
                    "output": response,
                    "execution_method": "legacy_code_generated"
                }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Agent {agent_id} does not have a valid execution method"
            )

        return response

    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Agent execution failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Agent execution failed: {str(e)}"
        )


async def process_agent_creation(
    agent_db_id: int,
    team_plan: Dict[str, Any],
) -> None:
    """Process agent creation in background."""
    from app.core.database import AsyncSessionLocal
    
    async with AsyncSessionLocal() as db:
        try:
            # Update status to active (simplified for now)
            await db.execute(
                text("UPDATE agents SET status = :status, updated_at = :updated_at WHERE id = :id"),
                {"status": AgentStatus.ACTIVE, "updated_at": normalize_datetime_for_db(utc_now()), "id": agent_db_id},
            )
            await db.commit()
            
            api_logger.info("Agent creation completed", agent_db_id=agent_db_id)
            
        except Exception as e:
            # Update status to error
            await db.execute(
                text("UPDATE agents SET status = :status, updated_at = :updated_at WHERE id = :id"),
                {"status": AgentStatus.ERROR, "updated_at": normalize_datetime_for_db(utc_now()), "id": agent_db_id},
            )
            await db.commit()
            
            api_logger.error("Agent creation failed", agent_db_id=agent_db_id, error=str(e))


async def process_agent_execution(
    agent_id: str,
    execution_id: str,
    input_data: Dict[str, Any],
) -> None:
    """Process agent execution in background."""
    # TODO: Implement actual agent execution logic
    api_logger.info(
        "Agent execution completed",
        agent_id=agent_id,
        execution_id=execution_id,
    )


async def extract_and_broadcast_variables(agent_id: str, progress_data: Dict[str, Any], test_id: Optional[str] = None, db: Optional[AsyncSession] = None):
    """
    Extract variable information from progress data and broadcast via WebSocket.

    This function analyzes progress data for variable resolution events and
    broadcasts them to connected WebSocket clients for real-time tracking.

    Args:
        agent_id: ID of the agent team
        progress_data: Progress data from agent execution
    """
    try:
        # Extract variables from progress data
        variables_resolved = progress_data.get("variables_resolved", [])
        context_data = progress_data.get("context_data", {})
        assignee = progress_data.get("assignee", "unknown")
        # Use current_step from progress data, fallback to step_index
        step_index = progress_data.get("current_step", progress_data.get("step_index", 0))
        # Convert to 0-based index if it's 1-based
        if step_index > 0:
            step_index = step_index - 1
        stage = progress_data.get("stage", "unknown")
        message = progress_data.get("message", "")
        step_name = progress_data.get("step_name", "")

        # Process explicitly resolved variables
        for variable_info in variables_resolved:
            if isinstance(variable_info, dict):
                variable_name = variable_info.get("name", "")
                variable_value = variable_info.get("value", "")

                if variable_name and variable_value:
                    await variable_tracker.track_variable_resolution(
                        agent_id=agent_id,
                        variable_name=variable_name,
                        variable_value=variable_value,
                        source_agent=assignee,
                        execution_step=step_index,
                        variable_type="inter-agent",
                        destination_agents=[],
                        metadata={
                            "stage": stage,
                            "message": message[:100],  # Truncate long messages
                            "extraction_method": "explicit_resolution"
                        },
                        test_id=test_id
                    )

        # Skip context data extraction - variables should only be tracked
        # when steps are completed with full content

        # Skip pattern matching - variables should only be tracked
        # when steps are completed with full content

        # Skip step-based variable creation - variables should only be tracked
        # when steps are completed with full content through _track_step_variables

        # Note: Database updates are now handled in _track_step_variables when steps complete
        # This avoids excessive database calls during progress updates
        if test_id:
            api_logger.debug(f"Progress update for test {test_id} - database will be updated when steps complete")
        else:
            api_logger.debug("No test_id provided for progress update")

    except Exception as e:
        api_logger.error(f"Error extracting and broadcasting variables: {str(e)}")
        # Don't raise the exception to avoid breaking the main execution flow


async def update_test_variables_in_db(test_id: str, agent_id: str, db: AsyncSession):
    """
    Update test history database with variable data from VariableTracker.

    This function merges new variable data with existing data in the database,
    ensuring that resolved variables update pending ones.

    Args:
        test_id: Test execution identifier
        agent_id: Agent identifier
        db: Database session
    """
    try:
        from app.services.websocket_service import variable_tracker
        import json

        api_logger.debug(f"Checking for variable data for test {test_id}")

        # Get stored variable data from VariableTracker
        stored_data = variable_tracker.get_stored_variables(test_id)

        if not stored_data:
            api_logger.debug(f"No variable data found in VariableTracker for test {test_id}")
            return

        api_logger.info(f"Found variable data for test {test_id}: {len(stored_data.get('context_placeholders_used', []))} placeholders")

        # Get existing data from database
        existing_query = "SELECT context_placeholders_used, team_member_interactions, context_summary FROM test_history WHERE test_id = :test_id"
        result = await db.execute(text(existing_query), {"test_id": test_id})
        existing_record = result.fetchone()

        if not existing_record:
            api_logger.warning(f"No test record found for test_id {test_id}")
            return

        # Merge variable data with existing data
        merged_placeholders = await _merge_variable_data(
            existing_record.context_placeholders_used,
            stored_data.get('context_placeholders_used', [])
        )

        merged_interactions = await _merge_interaction_data(
            existing_record.team_member_interactions,
            stored_data.get('team_member_interactions', [])
        )

        merged_summary = await _merge_summary_data(
            existing_record.context_summary,
            stored_data.get('context_summary', {})
        )

        # Prepare update data
        update_data = {
            "updated_at": normalize_datetime_for_db(utc_now()),
            "context_placeholders_used": json.dumps(merged_placeholders),
            "team_member_interactions": json.dumps(merged_interactions),
            "context_summary": json.dumps(merged_summary)
        }

        # Build update query
        set_clauses = []
        params = {"test_id": test_id}

        for key, value in update_data.items():
            set_clauses.append(f"{key} = :{key}")
            params[key] = value

        query = f"UPDATE test_history SET {', '.join(set_clauses)} WHERE test_id = :test_id"
        api_logger.debug(f"Executing merged update query for test {test_id}")

        result = await db.execute(text(query), params)
        await db.commit()

        api_logger.info(f"Successfully merged and updated variable data in database for test {test_id} (affected rows: {result.rowcount})")
        api_logger.debug(f"Merged data: {len(merged_placeholders)} placeholders, {len(merged_interactions)} interactions")

    except Exception as e:
        api_logger.error(f"Failed to update test variables in database for {test_id}: {str(e)}")
        # Don't raise the exception to avoid breaking the main execution flow


async def _merge_variable_data(existing_json: str, new_data: List[Dict]) -> List[Dict]:
    """
    Merge new variable data with existing data, updating resolved variables.

    Args:
        existing_json: JSON string of existing variable data
        new_data: List of new variable data

    Returns:
        Merged list of variable data
    """
    try:
        import json

        # Parse existing data
        existing_data = []
        if existing_json:
            try:
                existing_data = json.loads(existing_json) if isinstance(existing_json, str) else existing_json
                if not isinstance(existing_data, list):
                    existing_data = []
            except (json.JSONDecodeError, TypeError):
                existing_data = []

        # Create a map of existing variables by name for quick lookup
        existing_map = {}
        for item in existing_data:
            if isinstance(item, dict) and 'variable_name' in item:
                existing_map[item['variable_name']] = item

        # Add or update variables from new data
        for new_item in new_data:
            if isinstance(new_item, dict) and 'variable_name' in new_item:
                variable_name = new_item['variable_name']

                # If variable exists and new one has a value, update it
                if variable_name in existing_map:
                    existing_item = existing_map[variable_name]
                    # Update if new item has a non-empty value or is marked as complete
                    if (new_item.get('variable_value') and new_item['variable_value'].strip() and
                        new_item['variable_value'] != 'pending'):
                        existing_map[variable_name] = new_item
                        api_logger.debug(f"Updated existing variable: {variable_name}")
                    else:
                        api_logger.debug(f"Kept existing variable: {variable_name}")
                else:
                    # Add new variable
                    existing_map[variable_name] = new_item
                    api_logger.debug(f"Added new variable: {variable_name}")

        # Convert back to list
        merged_data = list(existing_map.values())
        return merged_data

    except Exception as e:
        api_logger.error(f"Error merging variable data: {str(e)}")
        return new_data  # Fallback to new data


async def _merge_interaction_data(existing_json: str, new_data: List[Dict]) -> List[Dict]:
    """
    Merge new interaction data with existing data.

    Args:
        existing_json: JSON string of existing interaction data
        new_data: List of new interaction data

    Returns:
        Merged list of interaction data
    """
    try:
        import json

        # Parse existing data
        existing_data = []
        if existing_json:
            try:
                existing_data = json.loads(existing_json) if isinstance(existing_json, str) else existing_json
                if not isinstance(existing_data, list):
                    existing_data = []
            except (json.JSONDecodeError, TypeError):
                existing_data = []

        # Simply append new interactions (they are typically unique by timestamp)
        merged_data = existing_data + new_data
        return merged_data

    except Exception as e:
        api_logger.error(f"Error merging interaction data: {str(e)}")
        return new_data  # Fallback to new data


async def _merge_summary_data(existing_json: str, new_data: Dict) -> Dict:
    """
    Merge new summary data with existing data.

    Args:
        existing_json: JSON string of existing summary data
        new_data: New summary data

    Returns:
        Merged summary data
    """
    try:
        import json

        # Parse existing data
        existing_data = {}
        if existing_json:
            try:
                existing_data = json.loads(existing_json) if isinstance(existing_json, str) else existing_json
                if not isinstance(existing_data, dict):
                    existing_data = {}
            except (json.JSONDecodeError, TypeError):
                existing_data = {}

        # Merge summary data
        merged_data = existing_data.copy()
        merged_data.update(new_data)

        return merged_data

    except Exception as e:
        api_logger.error(f"Error merging summary data: {str(e)}")
        return new_data  # Fallback to new data


async def update_individual_agent_output(test_id: str, agent_output_data: Dict[str, Any]):
    """
    Store individual agent output to the database for test tracking.

    Args:
        test_id: Test ID to update
        agent_output_data: Individual agent output data
    """
    try:
        from app.core.database import get_session
        from app.models.test_history import TestHistory
        from sqlalchemy import select

        async for db in get_session():
            # Get existing test history record
            result = await db.execute(
                select(TestHistory).where(TestHistory.test_id == test_id)
            )
            test_history = result.scalar_one_or_none()

            if not test_history:
                api_logger.warning(f"Test history not found for test_id: {test_id}")
                return

            # Get existing individual agent outputs or initialize empty list
            existing_outputs = test_history.individual_agent_outputs or []

            # Add new agent output
            existing_outputs.append(agent_output_data)

            # Update the test history record
            test_history.individual_agent_outputs = existing_outputs
            test_history.updated_at = normalize_datetime_for_db(utc_now())

            await db.commit()
            api_logger.debug(f"Updated individual agent output for test {test_id}")
            break  # Exit the async for loop

    except Exception as e:
        api_logger.error(f"Failed to update individual agent output: {str(e)}")


async def update_agent_execution_sequence(test_id: str, sequence_data: List[Dict[str, Any]]):
    """
    Store agent execution sequence to the database for test tracking.

    Args:
        test_id: Test ID to update
        sequence_data: Agent execution sequence data
    """
    try:
        from app.core.database import get_session
        from app.models.test_history import TestHistory
        from sqlalchemy import select

        async for db in get_session():
            # Get existing test history record
            result = await db.execute(
                select(TestHistory).where(TestHistory.test_id == test_id)
            )
            test_history = result.scalar_one_or_none()

            if not test_history:
                api_logger.warning(f"Test history not found for test_id: {test_id}")
                return

            # Update the agent execution sequence
            test_history.agent_execution_sequence = sequence_data
            test_history.updated_at = normalize_datetime_for_db(utc_now())

            await db.commit()
            api_logger.debug(f"Updated agent execution sequence for test {test_id}")
            break  # Exit the async for loop

    except Exception as e:
        api_logger.error(f"Failed to update agent execution sequence: {str(e)}")


async def update_agent_performance_metrics(test_id: str, performance_data: Dict[str, Any]):
    """
    Store agent performance metrics to the database for test tracking.

    Args:
        test_id: Test ID to update
        performance_data: Agent performance metrics data
    """
    try:
        from app.core.database import get_session
        from app.models.test_history import TestHistory
        from sqlalchemy import select

        async for db in get_session():
            # Get existing test history record
            result = await db.execute(
                select(TestHistory).where(TestHistory.test_id == test_id)
            )
            test_history = result.scalar_one_or_none()

            if not test_history:
                api_logger.warning(f"Test history not found for test_id: {test_id}")
                return

            # Get existing performance metrics or initialize empty dict
            existing_metrics = test_history.agent_performance_metrics or {}

            # Merge with new performance data
            existing_metrics.update(performance_data)

            # Update the test history record
            test_history.agent_performance_metrics = existing_metrics
            test_history.updated_at = normalize_datetime_for_db(utc_now())

            await db.commit()
            api_logger.debug(f"Updated agent performance metrics for test {test_id}")
            break  # Exit the async for loop

    except Exception as e:
        api_logger.error(f"Failed to update agent performance metrics: {str(e)}")

