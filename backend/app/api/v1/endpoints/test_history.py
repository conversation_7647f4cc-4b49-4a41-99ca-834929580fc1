"""
Test history API endpoints.
"""

import json
import uuid
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi import status as http_status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, desc

from app.api.dependencies.database import get_db
from app.api.dependencies.auth import get_current_active_user
from app.core.logging import api_logger
from app.services.logging_service import log_test_execution
from app.core.timezone_utils import (
    utc_now, normalize_datetime_for_db, normalize_datetime_for_api,
    to_utc, calculate_duration_ms
)
from app.models.user import User
from app.models.test_history import (
    TestHistory, TestHistoryCreate, TestHistoryUpdate,
    TestHistoryResponse, TestHistoryDetailResponse, TestHistoryListResponse,
    TestStatus
)


def normalize_status(status_value) -> str:
    """Normalize status value to ensure it matches the enum values."""
    if isinstance(status_value, str):
        # Convert to lowercase to match enum values
        normalized = status_value.lower()
        # Validate against enum values
        valid_statuses = [s.value for s in TestStatus]
        if normalized in valid_statuses:
            return normalized
        # If it's an uppercase enum name, convert it
        if status_value.upper() in [s.name for s in TestStatus]:
            return getattr(TestStatus, status_value.upper()).value
    return status_value

router = APIRouter()


def safe_json_loads(value):
    """Safely deserialize JSON fields from database."""
    if value is None:
        return None
    if isinstance(value, str):
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value
    return value


@router.post("/", response_model=TestHistoryResponse)
async def create_test_history(
    test_data: TestHistoryCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new test history record."""
    try:
        # Create test history record
        test_history = TestHistory(
            test_id=test_data.test_id,
            user_id=current_user.id,
            agent_id=test_data.agent_id,
            input_text=test_data.input_text,
            ai_config_override=test_data.ai_config_override,
            api_key_id=test_data.api_key_id,
            api_key_name=test_data.api_key_name,
            input_metadata=test_data.input_metadata,
            status=TestStatus.RUNNING
        )
        
        db.add(test_history)
        await db.commit()
        await db.refresh(test_history)
        
        api_logger.info(f"Test history created: {test_history.test_id} for user {current_user.id}")

        # Log test start event
        await log_test_execution(
            event_type="test_start",
            test_id=test_history.test_id,
            agent_id=test_history.agent_id,
            user_id=current_user.id,
            message=f"Test execution started for agent {test_history.agent_id}",
            metadata={
                "input_length": len(test_history.input_text),
                "api_key_id": test_history.api_key_id,
                "api_key_name": test_history.api_key_name,
                "has_ai_config_override": bool(test_history.ai_config_override)
            }
        )

        return TestHistoryResponse.model_validate(test_history)
        
    except Exception as e:
        api_logger.error(f"Failed to create test history: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create test history record"
        )


@router.put("/{test_id}", response_model=TestHistoryResponse)
async def update_test_history(
    test_id: str,
    update_data: TestHistoryUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update an existing test history record."""
    try:
        # Find the test history record
        result = await db.execute(
            text("SELECT * FROM test_history WHERE test_id = :test_id AND user_id = :user_id"),
            {"test_id": test_id, "user_id": current_user.id}
        )
        test_history_data = result.fetchone()
        
        if not test_history_data:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Test history not found"
            )
        
        # Prepare update data
        update_dict = update_data.model_dump(exclude_unset=True)
        if update_dict:
            update_dict["updated_at"] = normalize_datetime_for_db(utc_now())

            # Calculate execution duration if both timestamps are available
            if "completed_at" in update_dict and update_dict["completed_at"] is not None:
                # Get the current started_at from database
                started_at = test_history_data.started_at
                completed_at = update_dict["completed_at"]

                # Calculate duration using timezone-aware calculation
                calculated_duration = calculate_duration_ms(started_at, completed_at)

                # Use calculated duration if not provided or if provided duration seems incorrect
                if "execution_duration_ms" not in update_dict or update_dict["execution_duration_ms"] is None:
                    if calculated_duration is not None:
                        update_dict["execution_duration_ms"] = calculated_duration
                        api_logger.info(f"Calculated execution duration: {calculated_duration}ms for test {test_id}")
                elif calculated_duration is not None:
                    # Validate provided duration against calculated duration (allow 5% tolerance)
                    provided_duration = update_dict["execution_duration_ms"]
                    tolerance = max(1000, abs(calculated_duration) * 0.05)  # 5% or 1 second minimum
                    if abs(provided_duration - calculated_duration) > tolerance:
                        api_logger.warning(
                            f"Duration mismatch for test {test_id}: provided={provided_duration}ms, "
                            f"calculated={calculated_duration}ms, using calculated value"
                        )
                        update_dict["execution_duration_ms"] = calculated_duration
            
            # Build SET clause dynamically
            set_clauses = []
            params = {"test_id": test_id, "user_id": current_user.id}
            
            for key, value in update_dict.items():
                set_clauses.append(f"{key} = :{key}")
                # Handle datetime fields with timezone normalization
                if key in ["completed_at", "started_at"] and value is not None:
                    params[key] = normalize_datetime_for_db(to_utc(value))
                # Serialize JSON fields for SQLite storage
                elif key in ["execution_stages", "progress_updates", "response_metadata", "error_details", "input_metadata", "ai_config_override", "context_summary", "context_placeholders_used", "team_member_interactions"] and value is not None:
                    if isinstance(value, (dict, list)):
                        params[key] = json.dumps(value)
                    else:
                        params[key] = value
                else:
                    params[key] = value
            
            if set_clauses:
                query = f"UPDATE test_history SET {', '.join(set_clauses)} WHERE test_id = :test_id AND user_id = :user_id"
                await db.execute(text(query), params)
                await db.commit()
        
        # Fetch updated record
        result = await db.execute(
            text("SELECT * FROM test_history WHERE test_id = :test_id AND user_id = :user_id"),
            {"test_id": test_id, "user_id": current_user.id}
        )
        updated_data = result.fetchone()
        
        api_logger.info(f"Test history updated: {test_id} for user {current_user.id}")

        # Log test completion/failure events
        status = normalize_status(updated_data.status)
        if update_data.status:
            if update_data.status == "completed":
                await log_test_execution(
                    event_type="test_complete",
                    test_id=test_id,
                    agent_id=updated_data.agent_id,
                    user_id=current_user.id,
                    message=f"Test execution completed for agent {updated_data.agent_id}",
                    execution_time_ms=updated_data.execution_duration_ms,
                    metadata={
                        "output_length": len(updated_data.final_output) if updated_data.final_output else 0,
                        "execution_duration_ms": updated_data.execution_duration_ms,
                        "has_error": bool(updated_data.error_message)
                    }
                )
            elif update_data.status == "failed":
                await log_test_execution(
                    event_type="test_fail",
                    test_id=test_id,
                    agent_id=updated_data.agent_id,
                    user_id=current_user.id,
                    message=f"Test execution failed for agent {updated_data.agent_id}",
                    execution_time_ms=updated_data.execution_duration_ms,
                    metadata={
                        "error_message": updated_data.error_message,
                        "execution_duration_ms": updated_data.execution_duration_ms
                    },
                    error_code="test_execution_failed" if updated_data.error_message else None
                )

        return TestHistoryResponse(
            id=updated_data.id,
            test_id=updated_data.test_id,
            user_id=updated_data.user_id,
            agent_id=updated_data.agent_id,
            status=status,
            started_at=normalize_datetime_for_api(updated_data.started_at),
            completed_at=normalize_datetime_for_api(updated_data.completed_at),
            execution_duration_ms=updated_data.execution_duration_ms,
            ai_config_override=safe_json_loads(updated_data.ai_config_override),
            api_key_id=updated_data.api_key_id,
            api_key_name=updated_data.api_key_name,
            input_text=updated_data.input_text,
            final_output=updated_data.final_output,
            error_message=updated_data.error_message,
            created_at=normalize_datetime_for_api(updated_data.created_at)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to update test history: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update test history record"
        )


@router.get("/", response_model=TestHistoryListResponse)
async def list_test_history(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    agent_id: Optional[str] = Query(None, description="Filter by agent ID"),
    status: Optional[TestStatus] = Query(None, description="Filter by status"),
    search: Optional[str] = Query(None, description="Search in input text and agent ID"),
    sort_by: Optional[str] = Query("started_at", description="Sort field"),
    sort_order: Optional[str] = Query("desc", description="Sort order (asc/desc)"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get paginated list of test history for the current user."""
    try:
        offset = (page - 1) * limit

        # Build WHERE clause
        where_conditions = ["user_id = :user_id"]
        params = {"user_id": current_user.id, "limit": limit, "offset": offset}

        if agent_id:
            where_conditions.append("agent_id = :agent_id")
            params["agent_id"] = agent_id

        if status:
            where_conditions.append("status = :status")
            params["status"] = status

        if search:
            where_conditions.append("(input_text LIKE :search OR agent_id LIKE :search)")
            params["search"] = f"%{search}%"

        where_clause = " AND ".join(where_conditions)

        # Validate and build ORDER BY clause
        valid_sort_fields = ["started_at", "completed_at", "execution_duration_ms", "agent_id", "status"]
        if sort_by not in valid_sort_fields:
            sort_by = "started_at"

        if sort_order.lower() not in ["asc", "desc"]:
            sort_order = "desc"

        order_clause = f"ORDER BY {sort_by} {sort_order.upper()}"

        # Get total count
        count_query = f"SELECT COUNT(*) as total FROM test_history WHERE {where_clause}"
        count_result = await db.execute(text(count_query), params)
        total = count_result.fetchone().total

        # Get paginated results
        query = f"""
        SELECT * FROM test_history
        WHERE {where_clause}
        {order_clause}
        LIMIT :limit OFFSET :offset
        """
        
        result = await db.execute(text(query), params)
        test_histories = result.fetchall()
        
        tests = [
            TestHistoryResponse(
                id=row.id,
                test_id=row.test_id,
                user_id=row.user_id,
                agent_id=row.agent_id,
                status=normalize_status(row.status),
                started_at=normalize_datetime_for_api(row.started_at),
                completed_at=normalize_datetime_for_api(row.completed_at),
                execution_duration_ms=row.execution_duration_ms,
                ai_config_override=safe_json_loads(row.ai_config_override),
                api_key_id=row.api_key_id,
                api_key_name=row.api_key_name,
                input_text=row.input_text,
                final_output=row.final_output,
                error_message=row.error_message,
                created_at=normalize_datetime_for_api(row.created_at)
            )
            for row in test_histories
        ]
        
        has_next = (offset + limit) < total
        has_prev = page > 1
        
        return TestHistoryListResponse(
            tests=tests,
            total=total,
            page=page,
            limit=limit,
            has_next=has_next,
            has_prev=has_prev
        )
        
    except Exception as e:
        api_logger.error(f"Failed to list test history: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve test history"
        )


@router.get("/{test_id}", response_model=TestHistoryDetailResponse)
async def get_test_history_detail(
    test_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get detailed test history by test ID."""
    try:
        result = await db.execute(
            text("SELECT * FROM test_history WHERE test_id = :test_id AND user_id = :user_id"),
            {"test_id": test_id, "user_id": current_user.id}
        )
        test_history = result.fetchone()
        
        if not test_history:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Test history not found"
            )
        
        return TestHistoryDetailResponse(
            id=test_history.id,
            test_id=test_history.test_id,
            user_id=test_history.user_id,
            agent_id=test_history.agent_id,
            status=normalize_status(test_history.status),
            started_at=normalize_datetime_for_api(test_history.started_at),
            completed_at=normalize_datetime_for_api(test_history.completed_at),
            execution_duration_ms=test_history.execution_duration_ms,
            ai_config_override=safe_json_loads(test_history.ai_config_override),
            api_key_id=test_history.api_key_id,
            api_key_name=test_history.api_key_name,
            input_text=test_history.input_text,
            final_output=test_history.final_output,
            error_message=test_history.error_message,
            created_at=normalize_datetime_for_api(test_history.created_at),
            execution_stages=safe_json_loads(test_history.execution_stages),
            progress_updates=safe_json_loads(test_history.progress_updates),
            response_metadata=safe_json_loads(test_history.response_metadata),
            context_summary=safe_json_loads(getattr(test_history, 'context_summary', None)),
            context_placeholders_used=safe_json_loads(getattr(test_history, 'context_placeholders_used', None)),
            team_member_interactions=safe_json_loads(getattr(test_history, 'team_member_interactions', None)),
            individual_agent_outputs=safe_json_loads(getattr(test_history, 'individual_agent_outputs', None)),
            agent_execution_sequence=safe_json_loads(getattr(test_history, 'agent_execution_sequence', None)),
            inter_agent_variables=safe_json_loads(getattr(test_history, 'inter_agent_variables', None)),
            agent_performance_metrics=safe_json_loads(getattr(test_history, 'agent_performance_metrics', None)),
            error_details=safe_json_loads(test_history.error_details),
            input_metadata=safe_json_loads(test_history.input_metadata)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to get test history detail: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve test history detail"
        )


@router.delete("/{test_id}")
async def delete_test_history(
    test_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete a test history record."""
    try:
        result = await db.execute(
            text("DELETE FROM test_history WHERE test_id = :test_id AND user_id = :user_id"),
            {"test_id": test_id, "user_id": current_user.id}
        )
        
        if result.rowcount == 0:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Test history not found"
            )
        
        await db.commit()
        api_logger.info(f"Test history deleted: {test_id} for user {current_user.id}")
        
        return {"message": "Test history deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"Failed to delete test history: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete test history"
        )
