#!/usr/bin/env python3
"""
Debug script to test WebSocket and database synchronization.

This script tests the complete flow:
1. WebSocket variable broadcasting (successful)
2. Database writing at the same time (problematic)
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_websocket_database_sync():
    """Test the complete WebSocket and database sync flow."""
    print("🔄 Testing WebSocket and Database Synchronization...\n")
    
    try:
        from app.services.websocket_service import variable_tracker
        from app.core.database import AsyncSessionLocal
        from sqlalchemy import text
        
        test_id = f"debug_sync_{int(datetime.now().timestamp())}"
        agent_id = "debug_agent"
        
        print(f"📋 Test ID: {test_id}")
        print(f"🤖 Agent ID: {agent_id}")
        
        # Step 1: Create test record in database
        print("\n1️⃣ Creating test record in database...")
        async with AsyncSessionLocal() as db:
            insert_query = """
            INSERT INTO test_history (test_id, user_id, agent_id, input_text, status, started_at, created_at, updated_at)
            VALUES (:test_id, 1, :agent_id, 'Debug test input', 'running', datetime('now'), datetime('now'), datetime('now'))
            """
            
            await db.execute(text(insert_query), {
                "test_id": test_id,
                "agent_id": agent_id
            })
            await db.commit()
            print("  ✅ Test record created")
        
        # Step 2: Check initial database state
        print("\n2️⃣ Checking initial database state...")
        async with AsyncSessionLocal() as db:
            check_query = "SELECT context_placeholders_used FROM test_history WHERE test_id = :test_id"
            result = await db.execute(text(check_query), {"test_id": test_id})
            record = result.fetchone()
            
            initial_data = record.context_placeholders_used if record else None
            print(f"  📊 Initial context_placeholders_used: {initial_data}")
        
        # Step 3: Test variable tracking (this should trigger both WebSocket and database)
        print("\n3️⃣ Testing variable tracking...")
        
        test_variable_data = {
            "variable_name": "{planner.strategy}",
            "variable_value": "Comprehensive three-phase strategic approach: Research phase (weeks 1-2), Development phase (weeks 3-6), Testing phase (weeks 7-8)",
            "source_agent": "planner",
            "execution_step": 0,
            "variable_type": "inter-agent",
            "destination_agents": ["analyst", "executor"],
            "metadata": {
                "step_name": "strategic_planning",
                "test_type": "debug_sync",
                "is_complete": True
            }
        }
        
        print(f"  📤 Tracking variable: {test_variable_data['variable_name']}")
        print(f"  📝 Variable value: {test_variable_data['variable_value'][:100]}...")
        
        # This should trigger both WebSocket broadcast AND database write
        await variable_tracker.track_variable_resolution(
            agent_id=agent_id,
            variable_name=test_variable_data["variable_name"],
            variable_value=test_variable_data["variable_value"],
            source_agent=test_variable_data["source_agent"],
            execution_step=test_variable_data["execution_step"],
            variable_type=test_variable_data["variable_type"],
            destination_agents=test_variable_data["destination_agents"],
            metadata=test_variable_data["metadata"],
            test_id=test_id  # This should trigger database write
        )
        
        print("  ✅ Variable tracking completed")
        
        # Step 4: Check database state after tracking
        print("\n4️⃣ Checking database state after tracking...")
        async with AsyncSessionLocal() as db:
            check_query = "SELECT context_placeholders_used, updated_at FROM test_history WHERE test_id = :test_id"
            result = await db.execute(text(check_query), {"test_id": test_id})
            record = result.fetchone()
            
            if record and record.context_placeholders_used:
                placeholders = json.loads(record.context_placeholders_used)
                print(f"  ✅ Database updated! Found {len(placeholders)} placeholders")
                print(f"  📊 Updated at: {record.updated_at}")
                
                for placeholder in placeholders:
                    var_name = placeholder.get('variable_name', 'Unknown')
                    var_value = placeholder.get('variable_value', 'No value')
                    print(f"    - {var_name}: {var_value[:100]}...")
                    
                    # Check if this is our test variable
                    if var_name == test_variable_data["variable_name"]:
                        if var_value == test_variable_data["variable_value"]:
                            print(f"    ✅ Variable value matches exactly!")
                            return True
                        else:
                            print(f"    ❌ Variable value mismatch!")
                            print(f"      Expected: {test_variable_data['variable_value'][:100]}...")
                            print(f"      Got: {var_value[:100]}...")
                            return False
                
                print(f"  ❌ Test variable not found in database")
                return False
            else:
                print(f"  ❌ No placeholders found in database")
                return False
        
    except Exception as e:
        print(f"❌ Error testing WebSocket database sync: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_variable_tracker_storage():
    """Test VariableTracker internal storage."""
    print("\n📦 Testing VariableTracker internal storage...\n")
    
    try:
        from app.services.websocket_service import variable_tracker
        
        test_id = f"storage_test_{int(datetime.now().timestamp())}"
        
        print(f"📋 Test ID: {test_id}")
        
        # Track a variable
        print("\n1️⃣ Tracking variable in VariableTracker...")
        await variable_tracker.track_variable_resolution(
            agent_id="storage_test_agent",
            variable_name="{analyst.analysis}",
            variable_value="Detailed market analysis showing 70% mobile usage, 25% desktop, 5% tablet",
            source_agent="analyst",
            execution_step=1,
            variable_type="inter-agent",
            destination_agents=["writer", "reviewer"],
            metadata={
                "step_name": "market_analysis",
                "test_type": "storage_test",
                "is_complete": True
            },
            test_id=test_id
        )
        
        print("  ✅ Variable tracked")
        
        # Check internal storage
        print("\n2️⃣ Checking VariableTracker internal storage...")
        stored_data = variable_tracker.get_stored_variables(test_id)
        
        if stored_data:
            placeholders = stored_data.get('context_placeholders_used', [])
            interactions = stored_data.get('team_member_interactions', [])
            summary = stored_data.get('context_summary', {})
            
            print(f"  ✅ Internal storage found:")
            print(f"    - Placeholders: {len(placeholders)}")
            print(f"    - Interactions: {len(interactions)}")
            print(f"    - Summary: {summary}")
            
            if placeholders:
                first_placeholder = placeholders[0]
                print(f"    - First placeholder: {first_placeholder.get('variable_name')} = {first_placeholder.get('variable_value', '')[:50]}...")
                return True
            else:
                print(f"    ❌ No placeholders in internal storage")
                return False
        else:
            print(f"  ❌ No internal storage found for test {test_id}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing VariableTracker storage: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_database_write_method():
    """Test the _write_to_database_immediately method directly."""
    print("\n💾 Testing database write method directly...\n")
    
    try:
        from app.services.websocket_service import variable_tracker, VariableUpdate
        from app.core.database import AsyncSessionLocal
        from sqlalchemy import text
        from datetime import datetime, timezone
        
        test_id = f"direct_write_{int(datetime.now().timestamp())}"
        
        print(f"📋 Test ID: {test_id}")
        
        # Create test record
        print("\n1️⃣ Creating test record...")
        async with AsyncSessionLocal() as db:
            insert_query = """
            INSERT INTO test_history (test_id, user_id, agent_id, input_text, status, started_at, created_at, updated_at)
            VALUES (:test_id, 1, 'direct_test_agent', 'Direct write test', 'running', datetime('now'), datetime('now'), datetime('now'))
            """
            
            await db.execute(text(insert_query), {"test_id": test_id})
            await db.commit()
            print("  ✅ Test record created")
        
        # Create VariableUpdate object
        print("\n2️⃣ Creating VariableUpdate object...")
        variable_update = VariableUpdate(
            variable_name="{executor.output}",
            variable_value="Task execution completed successfully with 95% accuracy and optimal performance metrics",
            source_agent="executor",
            execution_step=2,
            timestamp=datetime.now(timezone.utc).isoformat(),
            variable_type="inter-agent",
            destination_agents=["reviewer", "reporter"],
            metadata={
                "step_name": "task_execution",
                "test_type": "direct_write",
                "is_complete": True
            }
        )
        
        print(f"  📝 Variable: {variable_update.variable_name}")
        print(f"  📝 Value: {variable_update.variable_value[:100]}...")
        
        # Call the database write method directly
        print("\n3️⃣ Calling _write_to_database_immediately...")
        await variable_tracker._write_to_database_immediately(test_id, variable_update)
        
        # Check database
        print("\n4️⃣ Checking database after direct write...")
        async with AsyncSessionLocal() as db:
            check_query = "SELECT context_placeholders_used FROM test_history WHERE test_id = :test_id"
            result = await db.execute(text(check_query), {"test_id": test_id})
            record = result.fetchone()
            
            if record and record.context_placeholders_used:
                placeholders = json.loads(record.context_placeholders_used)
                print(f"  ✅ Database write successful! Found {len(placeholders)} placeholders")
                
                for placeholder in placeholders:
                    var_name = placeholder.get('variable_name', 'Unknown')
                    var_value = placeholder.get('variable_value', 'No value')
                    print(f"    - {var_name}: {var_value[:100]}...")
                
                return True
            else:
                print(f"  ❌ Database write failed - no placeholders found")
                return False
        
    except Exception as e:
        print(f"❌ Error testing database write method: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all debug tests."""
    print("🚀 WebSocket Database Sync Debug Suite\n")
    
    # Test 1: Complete sync flow
    sync_success = await test_websocket_database_sync()
    
    # Test 2: VariableTracker storage
    storage_success = await test_variable_tracker_storage()
    
    # Test 3: Direct database write
    write_success = await test_database_write_method()
    
    print("\n📊 Debug Results Summary:")
    print(f"   Complete Sync Flow: {'✅ PASS' if sync_success else '❌ FAIL'}")
    print(f"   VariableTracker Storage: {'✅ PASS' if storage_success else '❌ FAIL'}")
    print(f"   Direct Database Write: {'✅ PASS' if write_success else '❌ FAIL'}")
    
    if sync_success and storage_success and write_success:
        print("\n🎉 All debug tests passed! WebSocket and database sync should be working.")
    else:
        print("\n⚠️ Some debug tests failed. Check the error messages above.")
        
        if not sync_success:
            print("\n🔍 Complete Sync Flow failed:")
            print("   - Check if track_variable_resolution is being called with test_id")
            print("   - Check if _write_to_database_immediately is being executed")
            print("   - Check database connection and permissions")
        
        if not storage_success:
            print("\n🔍 VariableTracker Storage failed:")
            print("   - Check if _store_variable_data is working correctly")
            print("   - Check internal storage mechanism")
        
        if not write_success:
            print("\n🔍 Direct Database Write failed:")
            print("   - Check database schema and table structure")
            print("   - Check SQL query syntax and parameters")
            print("   - Check database session and transaction handling")
    
    print("\n💡 Next steps:")
    print("1. Run this debug script to identify the exact failure point")
    print("2. Check backend logs for detailed error messages")
    print("3. Verify database schema and test_history table structure")
    print("4. Test with a real agent execution to confirm the fix")

if __name__ == "__main__":
    asyncio.run(main())
