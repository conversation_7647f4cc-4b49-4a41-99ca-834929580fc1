#!/usr/bin/env python3
"""
Debug script to test the real agent execution flow.

This script simulates the actual agent execution process to identify
where the variable tracking might be failing.
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_real_execution_flow():
    """Test the real agent execution flow with variable tracking."""
    print("🎯 Testing Real Agent Execution Flow...\n")
    
    try:
        from app.services.dynamic_loader import ConfigDrivenAgent
        from app.core.database import AsyncSessionLocal
        from sqlalchemy import text
        
        # Create a realistic agent configuration
        # The key is that variables must be in system_prompt to be discovered by VariableDiscoveryService
        # ConfigDrivenAgent expects team_plan structure
        agent_config = {
            "agent_id": "real_test_agent",
            "team_name": "Real Test Team",
            "team_plan": {
                "team_members": [
                    {
                        "role": "planner",
                        "system_prompt": "You are a strategic planner. Create a comprehensive strategic plan and output it as {planner.strategy}.",
                        "description": "Strategic planning specialist"
                    },
                    {
                        "role": "analyst",
                        "system_prompt": "You are an analyst. Analyze the {planner.strategy} and create detailed analysis as {analyst.analysis}.",
                        "description": "Analysis specialist"
                    }
                ],
                "workflow": {
                    "steps": [
                        {
                            "name": "strategic_planning",
                            "assignee": "planner",
                            "description": "Create strategic plan",
                            "prompt": "Create a comprehensive strategic plan for the given requirements."
                        },
                        {
                            "name": "analysis",
                            "assignee": "analyst",
                            "description": "Analyze the strategy",
                            "prompt": "Analyze the strategic plan and provide detailed insights."
                        }
                    ]
                }
            }
        }
        
        test_id = f"real_flow_{int(datetime.now().timestamp())}"
        
        print(f"📋 Test ID: {test_id}")
        print(f"🤖 Agent Config: {agent_config['team_name']}")
        
        # Step 1: Create test record
        print("\n1️⃣ Creating test record...")
        async with AsyncSessionLocal() as db:
            insert_query = """
            INSERT INTO test_history (test_id, user_id, agent_id, input_text, status, started_at, created_at, updated_at)
            VALUES (:test_id, 1, :agent_id, 'Real execution test', 'running', datetime('now'), datetime('now'), datetime('now'))
            """
            
            await db.execute(text(insert_query), {
                "test_id": test_id,
                "agent_id": agent_config["agent_id"]
            })
            await db.commit()
            print("  ✅ Test record created")
        
        # Step 2: Create ConfigDrivenAgent instance
        print("\n2️⃣ Creating ConfigDrivenAgent instance...")
        agent = ConfigDrivenAgent(agent_config)
        agent.set_test_id(test_id)  # This is crucial!
        
        print(f"  ✅ Agent created with test_id: {agent.test_id}")
        
        # Step 3: Test variable discovery
        print("\n3️⃣ Testing variable discovery...")
        discovered_vars_planner = await agent._get_discovered_variables_for_agent("planner")
        discovered_vars_analyst = await agent._get_discovered_variables_for_agent("analyst")
        
        print(f"  📊 Planner variables: {len(discovered_vars_planner)}")
        for var in discovered_vars_planner:
            print(f"    - {var.get('variable_name')}: {var.get('semantic_description')}")
        
        print(f"  📊 Analyst variables: {len(discovered_vars_analyst)}")
        for var in discovered_vars_analyst:
            print(f"    - {var.get('variable_name')}: {var.get('semantic_description')}")
        
        # Step 4: Simulate step completion with variable tracking
        print("\n4️⃣ Simulating step completion...")
        
        # Simulate planner step completion
        planner_response = """
        Strategic Plan:
        
        Based on the requirements, I recommend a comprehensive three-phase approach:
        
        Phase 1: Research and Analysis (Weeks 1-2)
        - Market research and competitive analysis
        - User requirements gathering
        - Technical feasibility assessment
        
        Phase 2: Development and Implementation (Weeks 3-6)
        - System design and architecture
        - Core feature development
        - Integration and testing
        
        Phase 3: Launch and Optimization (Weeks 7-8)
        - Beta testing and feedback collection
        - Performance optimization
        - Full production launch
        
        This strategic approach ensures comprehensive coverage while maintaining flexibility for adjustments.
        """
        
        print(f"  📝 Simulating planner step completion...")
        await agent._track_step_variables(
            step_name="strategic_planning",
            assignee="planner",
            ai_response=planner_response,
            step_index=0
        )
        
        print(f"  ✅ Planner step tracking completed")
        
        # Step 5: Check database after first step
        print("\n5️⃣ Checking database after planner step...")
        async with AsyncSessionLocal() as db:
            check_query = "SELECT context_placeholders_used FROM test_history WHERE test_id = :test_id"
            result = await db.execute(text(check_query), {"test_id": test_id})
            record = result.fetchone()
            
            if record and record.context_placeholders_used:
                placeholders = json.loads(record.context_placeholders_used)
                print(f"  ✅ Database updated! Found {len(placeholders)} placeholders")
                
                for placeholder in placeholders:
                    var_name = placeholder.get('variable_name', 'Unknown')
                    var_value = placeholder.get('variable_value', 'No value')
                    source_agent = placeholder.get('source_agent', 'Unknown')
                    print(f"    - {var_name} (from {source_agent}): {var_value[:100]}...")
                    
                    if var_name == "{planner.strategy}" and var_value != "pending":
                        print(f"    ✅ Planner variable resolved successfully!")
                    elif var_name == "{planner.strategy}":
                        print(f"    ❌ Planner variable still pending")
                        return False
            else:
                print(f"  ❌ No placeholders found after planner step")
                return False
        
        # Step 6: Simulate analyst step completion
        print("\n6️⃣ Simulating analyst step completion...")
        
        analyst_response = """
        Analysis Results:
        
        After analyzing the strategic plan, here are my key findings:
        
        Strengths:
        - Well-structured three-phase approach provides clear milestones
        - Comprehensive coverage of all critical aspects
        - Realistic timeline with adequate buffer for adjustments
        
        Risk Assessment:
        - Medium risk in Phase 2 due to technical complexity
        - Low risk in Phase 1 and 3 with proper planning
        
        Recommendations:
        - Allocate additional resources for Phase 2 development
        - Implement weekly progress reviews
        - Establish clear success metrics for each phase
        
        Overall Assessment: The strategic plan is solid and executable with proper resource allocation.
        """
        
        await agent._track_step_variables(
            step_name="analysis",
            assignee="analyst",
            ai_response=analyst_response,
            step_index=1
        )
        
        print(f"  ✅ Analyst step tracking completed")
        
        # Step 7: Final database check
        print("\n7️⃣ Final database check...")
        async with AsyncSessionLocal() as db:
            check_query = "SELECT context_placeholders_used FROM test_history WHERE test_id = :test_id"
            result = await db.execute(text(check_query), {"test_id": test_id})
            record = result.fetchone()
            
            if record and record.context_placeholders_used:
                placeholders = json.loads(record.context_placeholders_used)
                print(f"  ✅ Final database state: {len(placeholders)} placeholders")
                
                resolved_count = 0
                for placeholder in placeholders:
                    var_name = placeholder.get('variable_name', 'Unknown')
                    var_value = placeholder.get('variable_value', 'No value')
                    source_agent = placeholder.get('source_agent', 'Unknown')
                    
                    if var_value and var_value != "pending":
                        resolved_count += 1
                        print(f"    ✅ {var_name} (from {source_agent}): RESOLVED")
                    else:
                        print(f"    ❌ {var_name} (from {source_agent}): PENDING")
                
                print(f"  📊 Summary: {resolved_count}/{len(placeholders)} variables resolved")
                
                if resolved_count == len(placeholders) and resolved_count > 0:
                    print(f"  🎉 All variables successfully resolved and stored!")
                    return True
                else:
                    print(f"  ⚠️ Some variables not resolved or no variables found")
                    return False
            else:
                print(f"  ❌ No placeholders found in final check")
                return False
        
    except Exception as e:
        print(f"❌ Error testing real execution flow: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the real execution flow test."""
    print("🚀 Real Agent Execution Flow Debug\n")
    
    success = await test_real_execution_flow()
    
    print(f"\n📊 Test Result: {'✅ SUCCESS' if success else '❌ FAILURE'}")
    
    if success:
        print("\n🎉 Real execution flow is working correctly!")
        print("   - Variables are discovered properly")
        print("   - Step completion triggers variable tracking")
        print("   - Database is updated with resolved variables")
        print("   - Variable values are extracted from AI responses")
    else:
        print("\n⚠️ Real execution flow has issues:")
        print("   - Check if test_id is properly set on the agent")
        print("   - Check if _track_step_variables is being called")
        print("   - Check if variable discovery is working")
        print("   - Check if variable extraction is working")
    
    print("\n💡 Next steps:")
    print("1. If this test passes, the issue might be in the frontend test execution")
    print("2. Check if the frontend is properly calling the backend with test_id")
    print("3. Verify that the agent configuration has proper context_placeholders")
    print("4. Monitor backend logs during actual frontend testing")

if __name__ == "__main__":
    asyncio.run(main())
